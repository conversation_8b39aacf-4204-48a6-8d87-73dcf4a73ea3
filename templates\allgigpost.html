
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>All Gig Posts | GigGenius</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #004AAD;
            --primary-pink: #CD208B;
            --yellow: #FFD700;
            --text-dark: #000000;
            --text-light: #FFFFFF;
            --text-gray: #666;
            --light-blue: #e0efff;
            --border-light: #eef2f8;
            --card-bg: #ffffff;
            --hover-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        }

        html {
            font-size: 1rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            transition: all 0.3s ease-in-out;
        }

        body {
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            background: linear-gradient(135deg,
                #f8f9ff 0%,
                #f0f5ff 35%,
                #e8f0ff 65%,
                #e5edff 100%
            );
            color: var(--text-dark);
            min-height: 100vh;
            font-size: 15px;
            line-height: 1.6;
        }

    
        /* Main content area */
        .dashboard {
            display: flex;
            flex-direction: column;
            max-width: 1400px;
            margin: 20px auto;
            background-color: rgba(255, 255, 255, 0.98);
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            padding: 30px;
            position: relative;
            overflow: hidden;
        }

        .dashboard::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: linear-gradient(to right, var(--primary-blue), var(--primary-pink));
        }

        .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 1px solid var(--border-light);
        }


        @keyframes width-expand {
            from {
                width: 0;
            }
            to {
                width: 40px;
            }
        }

        /* Page Subtitle Styling */
        .page-subtitle {
            color: #6b7280;
            font-size: 16px;
            font-weight: 400;
            text-align: center;
            margin: 16px auto 32px auto;
            line-height: 1.6;
            max-width: 500px;
            font-family: 'Poppins', sans-serif;
            letter-spacing: 0.3px;
            opacity: 0.9;
        }

        /* Post job button */
        .post-job-btn {
            padding: 12px 24px;
            background: linear-gradient(to right, var(--primary-pink), #e91e63);
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(194, 24, 91, 0.2);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .post-job-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(194, 24, 91, 0.3);
        }

        .post-job-btn:active {
            transform: translateY(0);
            box-shadow: 0 2px 5px rgba(194, 24, 91, 0.2);
        }

        /* Search and filter section */
        .search-bar {
            display: flex;
            margin-bottom: 25px;
            gap: 15px;
            position: relative;
        }

        .search-container {
            flex: 1;
            position: relative;
            display: flex;
            align-items: center;
        }

        .search-container i {
            position: absolute;
            left: 18px;
            color: var(--primary-blue);
            font-size: 16px;
        }

        .search-bar input {
            flex: 1;
            padding: 14px 18px 14px 45px;
            border: 2px solid #e0e7ff;
            border-radius: 10px;
            font-size: 15px;
            width: 100%;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            font-family: 'Poppins', sans-serif;
        }

        .search-bar input:focus {
            border-color: var(--primary-blue);
            box-shadow: 0 4px 12px rgba(6, 77, 172, 0.1);
            outline: none;
        }

        .search-bar input::placeholder {
            color: #a0aec0;
        }

        .filter-button {
            padding: 0 20px;
            height: 50px;
            background-color: #ffffff;
            border: 2px solid var(--primary-blue);
            border-radius: 10px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 10px;
            color: var(--primary-blue);
            font-size: 15px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        }

        .filter-button i {
            font-size: 16px;
        }

        .filter-button:hover {
            background-color: #f0f7ff;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(6, 77, 172, 0.1);
        }

        .filter-button.active {
            background-color: var(--primary-blue);
            color: #ffffff;
            box-shadow: 0 4px 12px rgba(6, 77, 172, 0.2);
        }

        .filter-count {
            background-color: var(--primary-pink);
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 11px;
            font-weight: 600;
            margin-left: 5px;
            animation: bounce 0.5s ease;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-5px);
            }
            60% {
                transform: translateY(-2px);
            }
        }

        /* Ripple effect for buttons */
        .filter-button {
            position: relative;
            overflow: hidden;
        }

        .ripple {
            position: absolute;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.4);
            transform: scale(0);
            animation: ripple-animation 0.5s linear;
            pointer-events: none;
        }

        @keyframes ripple-animation {
            to {
                transform: scale(2.5);
                opacity: 0;
            }
        }

        .filters {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
            margin-bottom: 24px;
            display: none;
            animation: slideDown 0.3s ease-out;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .filters.show {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }

        .filter-group {
            flex: 1;
            min-width: 200px;
        }

        .filter-group label {
            display: block;
            font-size: 13px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .filter-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            color: #1f2937;
            background-color: white;
            transition: all 0.2s ease;
            cursor: pointer;
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%236B7280' viewBox='0 0 16 16'%3E%3Cpath d='M8 10.5l-4-4h8l-4 4z'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 12px center;
        }

        .filter-group select:hover {
            border-color: #064dac;
        }

        .filter-group select:focus {
            outline: none;
            border-color: #064dac;
            box-shadow: 0 0 0 3px rgba(6, 77, 172, 0.1);
        }

        /* Enhanced filter tags */
        .filter-tag {
            background: #f0f7ff;
            border: 1px solid #064dac;
            padding: 8px 12px;
            border-radius: 20px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 13px;
            color: #064dac;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .filter-tag:hover {
            background: #e0efff;
        }

        .filter-tag .remove-tag {
            width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: #064dac;
            color: white;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .filter-tag .remove-tag:hover {
            background: #d32f2f;
            transform: scale(1.1);
        }

        .actions {
            margin-top: 16px;
            display: none; /* Hide by default */
        }

        .actions.show {
            display: block;
            animation: fadeIn 0.3s ease-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .post-job-btn {
            padding: 10px 20px;
            background-color: #c2185b;
            color: white;
            border: none;
            border-radius: 4px;
            font-weight: 500;
            cursor: pointer;
        }

        .post-job-btn:hover {
            background-color: #c2185b;
            opacity: 0.9;
        }

        .status-tabs {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .selected-filters {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        /* Enhanced clear filters button */
        .clear-filters {
            padding: 10px 16px;
            background: #fff;
            border: 2px solid #e5e7eb;
            border-radius: 20px;
            color: #4b5563;
            font-weight: 500;
            transition: all 0.2s ease;
            display: none; /* Hide by default */
        }

        .clear-filters:hover {
            background: #f9fafb;
            border-color: #d1d5db;
            color: #1f2937;
        }

        .clear-filters.show {
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .clear-filters.show::before {
            content: '×';
            font-size: 18px;
            font-weight: bold;
        }

        /* Job listings */
        .job-list {
            margin-top: 30px;
            position: relative;
        }

        .job-list-empty {
            text-align: center;
            padding: 60px 0;
            color: var(--light-text);
        }

        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 60px 0;
        }

        .empty-state-icon {
            font-size: 60px;
            color: #e0e7ff;
            margin-bottom: 20px;
        }

        .empty-state-text {
            font-size: 18px;
            color: var(--light-text);
            margin-bottom: 25px;
        }

        .job-card {
            background: white;
            border: 3px solid #e5e7eb;
            border-radius: 20px;
            padding: 36px;
            margin-bottom: 40px;
            transition: all 0.3s ease;
            position: relative;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
            min-height: 450px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        @keyframes card-fade-in {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .job-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(to bottom, var(--primary-blue), var(--primary-pink));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .job-card:hover {
            box-shadow: 0 16px 40px rgba(0, 0, 0, 0.2);
            transform: translateY(-6px);
            border-color: var(--primary-blue);
            z-index: 10;
        }

        .job-card:hover::before {
            opacity: 1;
        }

        /* Distinct visual elements to prevent muddling */
        .job-card:nth-child(odd) {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-color: #d1d5db;
        }

        .job-card:nth-child(even) {
            background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
            border-color: #e5e7eb;
        }

        .job-card:nth-child(odd)::before {
            background: linear-gradient(to bottom, #3b82f6, #1e40af);
        }

        .job-card:nth-child(even)::before {
            background: linear-gradient(to bottom, #ec4899, #be185d);
        }

        /* Add subtle separator between cards */
        .job-card + .job-card {
            position: relative;
        }

        .job-card + .job-card::after {
            content: '';
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 3px;
            background: linear-gradient(to right, transparent, #e5e7eb, transparent);
            border-radius: 2px;
        }

        /* Add staggered animation for job cards */
        .job-card:nth-child(2) {
            animation-delay: 0.1s;
        }

        .job-card:nth-child(3) {
            animation-delay: 0.2s;
        }

        .job-card:nth-child(4) {
            animation-delay: 0.3s;
        }

        .job-card:nth-child(5) {
            animation-delay: 0.4s;
        }

        /* Simplified Card Sections */
        .published-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
            gap: 16px;
        }

        /* Simplified Job Details */
        .job-details-simple {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            margin: 16px 0;
        }

        .detail-tag {
            background: #f3f4f6;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 14px;
            color: #374151;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .detail-tag i {
            color: var(--primary-blue);
            font-size: 12px;
        }

        /* Statistics Row */
        .job-stats-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 12px;
            margin: 20px 0;
        }

        .stat-item {
            text-align: center;
            padding: 12px 8px;
            background: white;
            border-radius: 10px;
            border: 1px solid #e2e8f0;
            transition: all 0.2s ease;
        }

        .stat-item:hover {
            border-color: var(--primary-blue);
            box-shadow: 0 2px 8px rgba(0, 74, 173, 0.1);
        }

        .stat-number {
            font-size: 18px;
            font-weight: 700;
            color: var(--primary-blue);
            display: block;
        }

        .stat-label {
            font-size: 11px;
            color: #64748b;
            margin-top: 4px;
            font-weight: 500;
        }

        .job-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--primary-blue);
            margin-bottom: 12px;
            transition: color 0.2s ease;
            display: inline-block;
        }

        .job-title:hover {
            color: var(--primary-pink);
        }

        .job-meta {
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }

        .job-info {
            color: var(--light-text);
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .job-info i {
            color: var(--primary-pink);
            font-size: 14px;
            opacity: 0.8;
        }

        /* Enhanced job stats styling */
        .stats-actions-wrapper {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 15px;
        }

        .job-stats {
            flex: 1;
        }

        .stats-group {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }

        .stat-item {
            display: flex;
            align-items: center;
            color: var(--light-text);
            font-size: 14px;
        }

        .stat-number {
            font-weight: 600;
            color: var(--primary-blue);
            margin-right: 5px;
        }

        .new-badge {
            background-color: #ebf8ff;
            color: #3182ce;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            margin-left: 6px;
            border: 1px solid #bee3f8;
        }

        .hired-group {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        /* Job actions styling */
        .job-actions {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        /* Add tooltip for action buttons */
        .job-actions button {
            position: relative;
        }

        .job-actions button::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: -35px;
            left: 50%;
            transform: translateX(-50%);
            background-color: #2d3748;
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.2s ease;
            z-index: 100;
        }

        .job-actions button:hover::after {
            opacity: 1;
            visibility: visible;
        }

        .job-status {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
            transition: all 0.2s ease;
        }

        .status-badge.filled {
            background-color: #10b981;
            color: white;
            border: 1px solid #10b981;
        }

        .status-badge.closed {
            background-color: #ef4444;
            color: white;
            border: 1px solid #ef4444;
        }

        .status-badge.open {
            background-color: #10b981;
            color: white;
            border: 1px solid #10b981;
        }

        .status-badge.draft {
            background-color: #f59e0b;
            color: white;
            border: 1px solid #f59e0b;
        }

        .status-badge.published {
            background-color: #3b82f6;
            color: white;
            border: 1px solid #3b82f6;
        }

        .status-badge.paused {
            background-color: #f97316;
            color: white;
            border: 1px solid #f97316;
        }

        .status-badge.expired {
            background-color: #6b7280;
            color: white;
            border: 1px solid #6b7280;
        }

        .status-badge.unknown {
            background-color: #9ca3af;
            color: white;
            border: 1px solid #9ca3af;
        }

        .status-badge i {
            font-size: 10px;
        }

        .status-badge:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
        }

        /* Draft Card Styles - Removed background colors */

        .draft-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid #fbbf24;
        }

        .draft-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            background: #f59e0b;
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .draft-date {
            display: flex;
            align-items: center;
            gap: 6px;
            color: #92400e;
            font-size: 13px;
        }

        .draft-title {
            font-size: 18px;
            font-weight: 600;
            color: #92400e;
            margin-bottom: 12px;
            cursor: default;
        }

        .draft-preview {
            margin-bottom: 16px;
        }

        .draft-description {
            color: #78350f;
            font-size: 14px;
            line-height: 1.5;
            margin: 0;
        }

        .draft-description.incomplete {
            color: #a16207;
            font-style: italic;
        }

        .draft-completion {
            margin-bottom: 20px;
        }

        .completion-bar {
            width: 100%;
            height: 8px;
            background: #fde68a;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 8px;
        }

        .completion-progress {
            height: 100%;
            background: linear-gradient(90deg, #f59e0b, #d97706);
            transition: width 0.3s ease;
        }

        .completion-text {
            font-size: 12px;
            color: #92400e;
            font-weight: 500;
        }

        .draft-actions {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }

        .continue-draft-btn {
            background: #f59e0b;
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .continue-draft-btn:hover {
            background: #d97706;
            transform: translateY(-2px);
        }

        .publish-draft-btn {
            background: #10b981;
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .publish-draft-btn:hover:not(:disabled) {
            background: #059669;
            transform: translateY(-2px);
        }

        .publish-draft-btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
            transform: none;
        }

        .delete-draft-btn {
            background: #ef4444;
            color: white;
            border: none;
            padding: 10px 12px;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .delete-draft-btn:hover {
            background: #dc2626;
            transform: translateY(-2px);
        }

        /* Clean Card Styles */
        .job-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
            gap: 16px;
        }

        .job-header .job-title {
            flex: 1;
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: var(--primary-blue);
            transition: color 0.2s ease;
        }

        .job-header .job-title:hover {
            color: var(--primary-pink);
        }

        /* Clean Job Description */
        .job-description {
            margin-bottom: 16px;
            padding-bottom: 16px;
            border-bottom: 1px solid #f3f4f6;
        }

        .job-description p {
            color: #6b7280;
            font-size: 14px;
            line-height: 1.6;
            margin: 0;
        }

        .see-more-btn {
            background: none;
            border: none;
            color: #3b82f6;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            padding: 0;
            margin-left: 5px;
            text-decoration: underline;
        }

        .see-more-btn:hover {
            color: #1d4ed8;
        }

        /* User-Friendly Project Information */
        .project-info-user-friendly {
            margin-bottom: 20px;
        }

        /* Section Container - Clear Visual Separation */
        .section-container {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            transition: all 0.2s ease;
        }

        .section-container:hover {
            border-color: var(--primary-blue);
            box-shadow: 0 2px 12px rgba(0, 74, 173, 0.08);
        }

        /* Section Headers - Clear Identification */
        .section-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 2px solid #e2e8f0;
        }

        .section-header i {
            color: var(--primary-blue);
            font-size: 16px;
            width: 20px;
            text-align: center;
        }

        .section-header h4 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Job Details Grid - Easy to Scan */
        .details-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
        }

        .detail-box {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 16px;
            text-align: center;
            transition: all 0.2s ease;
        }

        .detail-box:hover {
            border-color: var(--primary-blue);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 74, 173, 0.1);
        }

        .detail-label {
            font-size: 14px;
            font-weight: 600;
            color: #6b7280;
            margin-bottom: 8px;
            display: block;
        }

        .detail-value {
            font-size: 18px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 4px;
            display: block;
        }

        .detail-note {
            font-size: 12px;
            color: #9ca3af;
            font-style: italic;
        }

        /* Client Information Grid - Easy to Read */
        .client-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 12px;
        }

        .client-detail {
            display: flex;
            flex-direction: column;
            gap: 4px;
            padding: 12px;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .client-detail:hover {
            border-color: var(--primary-blue);
            box-shadow: 0 2px 8px rgba(0, 74, 173, 0.1);
        }

        .client-label {
            font-size: 12px;
            font-weight: 600;
            color: #6b7280;
            margin-bottom: 4px;
        }

        .client-value {
            font-size: 14px;
            font-weight: 500;
            color: #1f2937;
        }

        /* Application Activity Grid - Visual Stats */
        .activity-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 16px;
        }

        .activity-item {
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 10px;
            padding: 16px;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
        }

        .activity-item:hover {
            border-color: var(--primary-blue);
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0, 74, 173, 0.15);
        }

        .activity-number {
            font-size: 24px;
            font-weight: 700;
            color: var(--primary-blue);
            display: block;
            margin-bottom: 4px;
        }

        .activity-label {
            font-size: 12px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .activity-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* Clean Actions Layout */
        .job-actions-clean {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 12px;
        }

        .job-actions-clean .post-date {
            color: #9ca3af;
            font-size: 13px;
            display: flex;
            align-items: center;
            gap: 6px;
            margin-left: auto;
        }

        .job-actions-clean .post-date i {
            font-size: 12px;
        }

        .job-budget {
            display: flex;
            align-items: center;
            gap: 6px;
            color: var(--primary-blue);
            font-weight: 600;
            font-size: 14px;
        }

        .view-proposals-btn {
            background: var(--primary-blue);
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .view-proposals-btn:hover {
            background: #1e40af;
            transform: translateY(-2px);
        }

        .edit-job-btn {
            background: #6b7280;
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .edit-job-btn:hover {
            background: #4b5563;
            transform: translateY(-2px);
        }

        .edit-job-btn-enhanced {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
        }

        .edit-job-btn-enhanced:hover {
            background: linear-gradient(135deg, #ff5252 0%, #d63031 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(255, 107, 107, 0.4);
        }

        .edit-job-btn-enhanced:active {
            transform: translateY(0);
            box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
        }

        .edit-job-btn-enhanced i {
            font-size: 13px;
        }

        /* Page Subtitle */
        .page-subtitle {
            color: #6b7280;
            font-size: 16px;
            margin: 8px 0 24px 0;
            font-weight: 400;
        }

        /* Job Description Preview */
        .job-description-preview {
            margin: 16px 0;
            padding: 12px 0;
            border-bottom: 1px solid #f3f4f6;
        }

        .job-description-preview p {
            color: #4b5563;
            font-size: 14px;
            line-height: 1.6;
            margin: 0;
        }

        /* Job Details Grid */
        .job-details-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
            margin: 16px 0;
            padding: 16px 0;
            border-bottom: 1px solid #f3f4f6;
        }

        .detail-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .detail-item i {
            color: var(--primary-blue);
            width: 16px;
            text-align: center;
        }

        .detail-label {
            color: #6b7280;
            font-weight: 500;
        }

        .detail-value {
            color: #1f2937;
            font-weight: 600;
        }

        .skills-container {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            margin-top: 5px;
        }

        .skill-tag-inline {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-pink) 100%);
            color: white;
            padding: 4px 10px;
            border-radius: 15px;
            font-size: 11px;
            font-weight: 500;
            display: inline-block;
            white-space: nowrap;
        }

        .no-skills {
            color: #999;
            font-style: italic;
            font-size: 12px;
        }

        /* Professional Icon Colors */
        .detail-label i.bi-currency-dollar {
            color: #10b981; /* Green for money/budget */
        }

        .detail-label i.bi-bar-chart {
            color: #3b82f6; /* Blue for analytics/project size */
        }

        .detail-label i.bi-briefcase {
            color: #8b5cf6; /* Purple for job type */
        }

        .detail-label i.bi-tags {
            color: #f59e0b; /* Orange for category/tags */
        }

        .client-label i.bi-person-circle {
            color: #f59e0b; /* Orange for person/client */
        }

        .client-label i.bi-globe {
            color: #06b6d4; /* Cyan for global/location */
        }

        .client-label i.bi-calendar-date {
            color: #ef4444; /* Red for dates */
        }

        .info-label i.bi-target {
            color: #ec4899; /* Pink for targets/experience */
        }

        .info-label i.bi-clock {
            color: #f97316; /* Orange for time/availability */
        }

        .info-label i.bi-geo-alt {
            color: #84cc16; /* Lime green for location */
        }

        /* Section header icon colors */
        .section-header i.bi-info-circle-fill {
            color: #3b82f6; /* Blue for information */
        }

        .section-header i.bi-person-badge {
            color: #f59e0b; /* Orange for person */
        }

        .section-header i.bi-gear-fill {
            color: #8b5cf6; /* Purple for settings/skills */
        }

        .skills-header i.bi-star-fill {
            color: #fbbf24; /* Yellow for stars */
        }

        .skills-header i.bi-code-slash {
            color: #10b981; /* Green for code */
        }

        .skills-header i.bi-list-check {
            color: #06b6d4; /* Cyan for lists */
        }

        /* Status Badge Icon Colors */
        .status-badge.draft i {
            color: #f59e0b; /* Orange for draft */
        }

        .status-badge.published i {
            color: #10b981; /* Green for published */
        }

        .status-badge.filled i {
            color: #3b82f6; /* Blue for filled */
        }

        .status-badge.closed i {
            color: #ef4444; /* Red for closed */
        }

        .status-badge.paused i {
            color: #8b5cf6; /* Purple for paused */
        }

        .status-badge.unknown i {
            color: #6b7280; /* Gray for unknown */
        }

        /* Filter and Search Icon Colors */
        .search-container i.bi-search {
            color: #6b7280; /* Gray for search */
        }

        .filter-button i.bi-funnel {
            color: #8b5cf6; /* Purple for filter */
        }

        /* Navigation and Action Button Icon Colors */
        .post-job-btn i.bi-briefcase-fill {
            color: #ffffff; /* White for contrast on button */
        }

        .apply-btn i.bi-send-fill {
            color: #ffffff; /* White for contrast on button */
        }

        .edit-job-btn-enhanced i.bi-pencil-square {
            color: #ffffff; /* White for contrast on button */
        }

        /* Filter Label Icon Colors */
        .filter-group label i.bi-people {
            color: #f59e0b; /* Orange for people */
        }

        .filter-group label i.bi-eye {
            color: #06b6d4; /* Cyan for visibility */
        }

        .filter-group label i.bi-kanban {
            color: #8b5cf6; /* Purple for status */
        }

        .filter-group label i.bi-currency-dollar {
            color: #10b981; /* Green for money */
        }

        .skills-box {
            grid-column: 1 / -1; /* Take full width */
        }

        /* Client Information */
        .client-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 16px 0;
            padding: 12px 0;
            border-bottom: 1px solid #f3f4f6;
        }

        .client-details {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .client-name {
            color: #1f2937;
            font-weight: 600;
            font-size: 14px;
        }

        .client-location {
            display: flex;
            align-items: center;
            gap: 4px;
            color: #6b7280;
            font-size: 13px;
        }

        .post-date {
            display: flex;
            align-items: center;
            gap: 6px;
            color: #6b7280;
            font-size: 13px;
        }

        /* Enhanced Job Actions */
        .apply-btn {
            background: var(--primary-blue);
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .apply-btn:hover {
            background: #1e40af;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
        }

        .view-details-btn {
            background: #f3f4f6;
            color: #374151;
            border: none;
            padding: 10px 16px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .view-details-btn:hover {
            background: #e5e7eb;
            transform: translateY(-2px);
        }

        /* Skills & Requirements Section */
        .skills-requirements-grid {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .skills-section {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 16px;
        }

        .skills-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 12px;
        }

        .skills-header i {
            color: var(--primary-blue);
            font-size: 14px;
        }

        .skills-title {
            font-size: 14px;
            font-weight: 600;
            color: #374151;
        }

        .skills-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .skill-tag {
            background: linear-gradient(135deg, #dbeafe, #bfdbfe);
            color: #1e40af;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 600;
            border: 1px solid #93c5fd;
            transition: all 0.2s ease;
        }

        .skill-tag:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(30, 64, 175, 0.2);
        }

        .tech-tag {
            background: linear-gradient(135deg, #fce7f3, #fbcfe8);
            color: #be185d;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 600;
            border: 1px solid #f9a8d4;
            transition: all 0.2s ease;
        }

        .tech-tag:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(190, 24, 93, 0.2);
        }

        .requirements-section {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 16px;
        }

        .requirements-text {
            color: #6b7280;
            font-size: 14px;
            line-height: 1.6;
            margin-top: 8px;
        }

        /* Additional Information Section */
        .additional-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
        }

        .info-detail {
            display: flex;
            flex-direction: column;
            gap: 4px;
            padding: 12px;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .info-detail:hover {
            border-color: var(--primary-blue);
            box-shadow: 0 2px 8px rgba(0, 74, 173, 0.1);
        }

        .info-label {
            font-size: 12px;
            font-weight: 600;
            color: #6b7280;
        }

        .info-value {
            font-size: 14px;
            font-weight: 500;
            color: #1f2937;
        }



        /* Stats Layout Updates */
        .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
        }

        .stat-label {
            font-size: 12px;
            color: #6b7280;
            font-weight: 500;
        }

        .job-stats {
            display: flex;
            gap: 32px;
        }

        .stats-group {
            display: flex;
            align-items: center;
            gap: 32px;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 6px;
            color: #4a5568;
            font-size: 14px;
        }

        .hired-group {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .stat-number {
            font-weight: 600;
            color: #2d3748;
        }

        .new-badge {
            background-color: #ebf5ff;
            color: #064dac;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-date {
            color: #666;
            font-size: 14px;
            margin-left: auto;
            padding-left: 24px;
        }

        .stats-actions-wrapper {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 16px;
            border-top: 1px solid #eef2f6;
        }

        .job-actions {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .reuse-posting-btn, .edit-draft-btn {
            background: linear-gradient(to right, var(--primary-pink), #e91e63);
            color: white;
            border: none;
            padding: 10px 18px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 3px 8px rgba(194, 24, 91, 0.15);
        }

        .reuse-posting-btn i, .edit-draft-btn i {
            font-size: 14px;
        }

        .reuse-posting-btn:hover, .edit-draft-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 12px rgba(194, 24, 91, 0.25);
        }

        .reuse-posting-btn:active, .edit-draft-btn:active {
            transform: translateY(0);
            box-shadow: 0 2px 5px rgba(194, 24, 91, 0.15);
        }

        .more-options-btn {
            background-color: #ffffff;
            color: var(--primary-blue);
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 10px;
            height: 40px;
            width: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .more-options-btn:hover {
            background-color: #f8fafc;
            border-color: #cbd5e0;
            transform: translateY(-2px);
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.05);
        }

        .more-options-btn i {
            color: var(--primary-blue);
            font-size: 16px;
        }

        /* Pagination styles */
        .pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 40px;
            padding-top: 24px;
            border-top: 1px solid var(--border-light);
            animation: fade-in 0.5s ease-out 0.3s both;
        }

        @keyframes fade-in {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        .pagination-info {
            color: var(--light-text);
            font-size: 14px;
            font-weight: 500;
            background-color: #f8fafc;
            padding: 8px 16px;
            border-radius: 20px;
            border: 1px solid #e2e8f0;
        }

        .pagination-info span {
            color: var(--primary-blue);
            font-weight: 600;
        }

        .pagination-controls {
            display: flex;
            gap: 12px;
        }

        .pagination-button {
            padding: 12px 24px;
            border: 2px solid #e2e8f0;
            background: white;
            border-radius: 10px;
            color: var(--primary-blue);
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
            position: relative;
            overflow: hidden;
        }

        .pagination-button i {
            font-size: 12px;
            transition: transform 0.3s ease;
        }

        .pagination-button:first-child i {
            margin-right: 4px;
        }

        .pagination-button:last-child i {
            margin-left: 4px;
        }

        .pagination-button:hover:not(:disabled) {
            background-color: #f0f7ff;
            border-color: var(--primary-blue);
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(6, 77, 172, 0.1);
        }

        .pagination-button:hover:not(:disabled):first-child i {
            transform: translateX(-3px);
        }

        .pagination-button:hover:not(:disabled):last-child i {
            transform: translateX(3px);
        }

        .pagination-button:active:not(:disabled) {
            transform: translateY(0);
            box-shadow: 0 2px 5px rgba(6, 77, 172, 0.1);
        }

        .page-info {
            padding: 8px 16px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            font-weight: 500;
            color: #495057;
            margin: 0 8px;
            font-size: 14px;
        }

        .pagination-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .pagination-button {
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .pagination-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            border-color: #e2e8f0;
            color: #a0aec0;
        }

        /* Add ripple effect to pagination buttons */
        .pagination-button .ripple {
            background-color: rgba(6, 77, 172, 0.1);
        }

        /* Filter select styling */
        .filter-select {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            font-family: 'Poppins', sans-serif;
            font-size: 14px;
            color: var(--dark-text);
            background-color: white;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23064dac' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 15px center;
            background-size: 15px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .filter-select:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 3px rgba(6, 77, 172, 0.1);
        }

        .filter-select:hover {
            border-color: #cbd5e0;
        }

        /* Empty state styling */
        .no-jobs-found {
            text-align: center;
            padding: 80px 0;
            color: var(--light-text);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            animation: fade-in-up 0.6s ease-out;
            background: linear-gradient(135deg, #ffffff, #f8faff);
            border-radius: 16px;
            border: 1px dashed #d4e2ff;
            margin: 20px 0;
        }

        @keyframes fade-in-up {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .no-jobs-found i {
            font-size: 70px;
            color: #c2d5f2;
            margin-bottom: 25px;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-10px);
            }
            100% {
                transform: translateY(0px);
            }
        }

        .no-jobs-found h3 {
            font-size: 24px;
            color: var(--primary-blue);
            margin-bottom: 15px;
            font-weight: 600;
        }

        .no-jobs-found p {
            font-size: 16px;
            color: var(--light-text);
            margin-bottom: 30px;
            max-width: 500px;
            line-height: 1.6;
        }

        .no-jobs-found .post-job-btn {
            padding: 14px 28px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .no-jobs-found .post-job-btn:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 8px 20px rgba(194, 24, 91, 0.3);
        }

        /* No results found state */
        .no-results-found {
            text-align: center;
            padding: 60px 0;
            color: var(--light-text);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            animation: fade-in-up 0.6s ease-out;
            background: linear-gradient(135deg, #ffffff, #f8faff);
            border-radius: 16px;
            border: 1px dashed #d4e2ff;
            margin: 20px 0;
        }

        .no-results-found i {
            font-size: 60px;
            color: #c2d5f2;
            margin-bottom: 20px;
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 0.8;
            }
            50% {
                transform: scale(1.1);
                opacity: 1;
            }
            100% {
                transform: scale(1);
                opacity: 0.8;
            }
        }

        .no-results-found h3 {
            font-size: 22px;
            color: var(--primary-blue);
            margin-bottom: 12px;
            font-weight: 600;
        }

        .no-results-found p {
            font-size: 15px;
            color: var(--light-text);
            margin-bottom: 25px;
            max-width: 450px;
            line-height: 1.6;
        }

        .clear-filters-btn {
            padding: 12px 24px;
            background: linear-gradient(to right, var(--primary-blue), #3182ce);
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(6, 77, 172, 0.2);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .clear-filters-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(6, 77, 172, 0.3);
        }

        .clear-filters-btn:active {
            transform: translateY(0);
            box-shadow: 0 2px 5px rgba(6, 77, 172, 0.2);
        }

        /* Responsive adjustments */
        @media (max-width: 1200px) {
            .nav-links {
                gap: 0.5rem;
            }

            .nav-links a {
                padding: 0.5rem 0.7rem;
                font-size: 1rem;
            }
        }

        @media (max-width: 992px) {
            .navbar-left {
                padding-left: 0.5rem;
            }

            .right-section {
                padding-right: 0.5rem;
            }

            .logo img {
                width: 3.2rem;
                height: 3.2rem;
            }

            .logo h1 {
                font-size: 1.4rem;
            }

            .nav-links {
                display: none;
            }

            .mobile-menu-btn {
                display: block;
                margin-left: 0.5rem;
            }

            .nav-links.active {
                display: flex;
                flex-direction: column;
                position: absolute;
                top: 4.5rem;
                left: 0;
                right: 0;
                background: white;
                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                z-index: 1000;
                padding: 0.5rem 0;
                width: 100%;
                align-items: flex-start;
                margin: 0;
            }

            .nav-links.active a {
                width: 100%;
                padding: 0.7rem 1.2rem;
                margin: 0;
            }
        }

        @media (max-width: 480px) {
            .navbar-left {
                padding-left: 0.3rem;
            }

            .right-section {
                padding-right: 0.3rem;
                gap: 0.8rem;
            }

            .logo img {
                width: 3rem;
                height: 3rem;
            }

            .logo h1 {
                font-size: 1.2rem;
                margin-left: 0.3rem;
                margin-right: 0.3rem;
            }
        }

        /* Navbar Styles */
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            height: 4.5rem;
            position: relative;
            width: 100%;
            max-width: 100%;
        }

        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--primary-blue);
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .mobile-menu-btn:hover {
            color: var(--primary-pink);
            background-color: rgba(0, 74, 173, 0.05);
        }

        .navbar-left {
            display: flex;
            align-items: center;
            gap: 2rem;
            padding-left: 0;
            flex: 1;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.8rem;
            text-decoration: none;
        }

        .logo img {
            width: 3.5rem;
            height: 3.5rem;
            border-radius: 50%;
            object-fit: cover;
        }

        .logo h1 {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-pink);
            margin: 0;
            transition: color 0.3s ease;
        }

        .logo:hover h1 {
            color: var(--primary-blue);
        }

        .nav-links {
            display: flex;
            gap: 1rem;
            align-items: center;
            height: 100%;
            margin: 0;
        }

        .nav-links a {
            color: var(--primary-blue);
            text-decoration: none;
            padding: 0.5rem 1rem;
            font-size: 1rem;
            font-weight: 500;
            position: relative;
            white-space: nowrap;
        }

        .nav-links a:hover, .nav-links a.active {
            color: var(--primary-pink);
        }

        .nav-links a:after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: 0;
            left: 0;
            background-color: var(--primary-pink);
            transition: width 0.3s ease;
        }

        .nav-links a:hover:after, .nav-links a.active:after {
            width: 100%;
        }

        .nav-dropdown {
            position: relative;
            display: inline-block;
            margin: 0;
        }

        .nav-dropbtn {
            font-weight: 500;
            font-size: 1rem;
            color: var(--primary-blue);
            background: none;
            border: none;
            padding: 0.5rem 1rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.3rem;
            position: relative;
            white-space: nowrap;
        }

        .nav-dropbtn:hover {
            color: var(--primary-pink);
        }

        .nav-dropbtn:after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: 0;
            left: 0;
            background-color: var(--primary-pink);
            transition: width 0.3s ease;
        }

        .nav-dropbtn:hover:after {
            width: 100%;
        }

        .nav-dropdown-content {
            display: none;
            position: absolute;
            background-color: #fff;
            min-width: 200px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            border-radius: 8px;
            z-index: 1001;
            top: 100%;
            left: 0;
            margin-top: 0.5rem;
        }

        .nav-dropdown-content a {
            color: var(--primary-blue);
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            font-size: 0.9rem;
        }

        .nav-dropdown-content a:hover {
            background-color: #f9f9f9;
            color: var(--primary-pink);
        }

        .nav-dropdown-content a:after {
            display: none;
        }

        .nav-dropdown.active .nav-dropdown-content {
            display: block;
        }

        /* Right section container */
        .right-section {
            display: flex;
            align-items: center;
            gap: 2rem;
            padding-right: 0;
            height: 100%;
        }

        /* Auth buttons container */
        .auth-buttons {
            display: flex;
            align-items: center;
            gap: 1.5rem; /* Increased gap between notification and profile */
        }

        /* Enhanced Notification Styles */
        .notification-icon {
            position: relative;
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .notification-icon:hover {
            background-color: rgba(0, 74, 173, 0.1);
        }

        .notification-icon i {
            font-size: 1.3rem;
            color: #4a5568;
        }

        #notification-count {
            position: absolute;
            top: 2px;
            right: 2px;
            background-color: var(--primary-pink);
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
            font-weight: 600;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .notification-dropdown {
            position: absolute;
            top: 60px;
            right: 10px;
            width: 380px;
            max-height: 500px;
            overflow-y: auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
            z-index: 1000;
            display: none;
            border: 1px solid rgba(0,0,0,0.08);
            animation: dropdown-fade 0.2s ease-out;
        }

        @media (max-width: 576px) {
            .notification-dropdown {
                width: calc(100vw - 40px);
                right: -100px;
                max-height: 400px;
            }
        }

        @media (max-width: 480px) {
            .notification-dropdown {
                right: -150px;
            }
        }

        @keyframes dropdown-fade {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .notification-dropdown.show {
            display: block;
        }

        .notification-header {
            padding: 18px 20px;
            border-bottom: 1px solid rgba(0,0,0,0.08);
            font-weight: 600;
            font-size: 1rem;
            color: var(--neutral-900);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .notification-header-actions {
            font-size: 0.8rem;
            color: var(--primary-blue);
            cursor: pointer;
            transition: color 0.2s ease;
        }

        .notification-header-actions:hover {
            color: var(--primary-pink);
        }

        .notification-item {
            padding: 16px 20px;
            border-bottom: 1px solid rgba(0,0,0,0.05);
            display: flex;
            align-items: flex-start;
            gap: 12px;
            transition: background-color 0.2s ease;
            cursor: pointer;
        }

        .notification-item:hover {
            background-color: #f8f9fa;
        }

        .notification-item.unread {
            background-color: rgba(0, 74, 173, 0.02);
            border-left: 3px solid var(--primary-blue);
        }

        .notification-item.unread:hover {
            background-color: rgba(0, 74, 173, 0.08);
        }

        .notification-icon-wrapper {
            margin-right: 15px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-light-blue), var(--primary-light-pink));
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .notification-icon-wrapper i {
            color: white;
            font-size: 1rem;
        }

        .notification-content {
            flex: 1;
            min-width: 0;
        }

        .notification-content p {
            margin: 0 0 8px 0;
            font-size: 0.9rem;
            line-height: 1.4;
            color: #333;
        }

        .notification-content span {
            font-size: 0.8rem;
            color: #666;
        }

        .empty-notifications {
            padding: 40px 20px;
            text-align: center;
            color: #666;
        }

        .empty-notifications i {
            font-size: 2rem;
            color: #ddd;
            margin-bottom: 10px;
        }

        /* Profile button */
        .profile-button {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            cursor: pointer;
            border: 2px solid rgba(0, 74, 173, 0.2);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
        }

        .profile-button:hover {
            border-color: var(--primary-pink);
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0, 74, 173, 0.15);
        }

        .profile-button img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* Profile dropdown */
        .profile-dropdown {
            position: relative;
            display: inline-block;
        }

        .profile-dropdown-content {
            display: none;
            position: absolute;
            right: 0;
            top: 60px;
            background-color: #fff;
            min-width: 220px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            border-radius: 8px;
            z-index: 1001;
            border: 1px solid rgba(0,0,0,0.1);
        }

        .profile-dropdown-content a {
            color: var(--primary-blue);
            padding: 12px 16px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .profile-dropdown-content a i {
            width: 20px;
            text-align: center;
        }

        .profile-dropdown-content a:hover {
            background-color: #f9f9f9;
            color: var(--primary-pink);
        }

        .dropdown-divider {
            height: 1px;
            background-color: #eee;
            margin: 8px 0;
        }

        .logout-option {
            border-top: 1px solid #eee;
        }

        .logout-option:hover {
            background-color: #fff5f5 !important;
            color: #dc3545 !important;
        }

        /* Show dropdown on click */
        .profile-dropdown.active .profile-dropdown-content {
            display: block;
        }

        /* Custom scrollbar for notification dropdown */
        .notification-dropdown::-webkit-scrollbar {
            width: 6px;
        }

        .notification-dropdown::-webkit-scrollbar-track {
            background: transparent;
        }

        .notification-dropdown::-webkit-scrollbar-thumb {
            background-color: rgba(0,0,0,0.2);
            border-radius: 3px;
        }

        .notification-dropdown::-webkit-scrollbar-thumb:hover {
            background-color: rgba(0,0,0,0.3);
        }

        /* Mobile responsive styles */
        @media (max-width: 768px) {
            .navbar {
                padding: 0 0.5rem;
                height: 3.8rem;
                display: flex;
                align-items: center;
                justify-content: space-between;
                background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
                border-bottom: 1px solid rgba(0, 74, 173, 0.08);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            }

            .navbar-left {
                padding-left: 2.2rem;
                position: relative;
                display: flex;
                align-items: center;
                flex: 1;
                min-width: 0;
                max-width: calc(100% - 140px);
            }

            .nav-links {
                display: none;
            }

            .mobile-menu-btn {
                display: flex;
                position: absolute;
                left: 0.4rem;
                top: 50%;
                transform: translateY(-50%);
                z-index: 1001;
            }

            .nav-links.active {
                display: flex;
                flex-direction: column;
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
                box-shadow: 0 0 50px rgba(0, 0, 0, 0.1);
                z-index: 9999;
                padding: 2rem 0;
                overflow-y: auto;
                -webkit-overflow-scrolling: touch;
                scroll-behavior: smooth;
            }

            .nav-links.active a {
                width: calc(100% - 2rem);
                padding: 1rem 1.5rem;
                margin: 0.5rem 1rem;
                border-radius: 8px;
                transition: all 0.3s ease;
                font-weight: 500;
                font-size: 1rem;
                color: #333;
                text-decoration: none;
                display: flex;
                align-items: center;
                gap: 0.8rem;
                background: white;
                border: 1px solid #e0e4e8;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            }

            .nav-links.active a:hover {
                background: var(--primary-blue);
                color: white;
                border-color: var(--primary-blue);
                box-shadow: 0 4px 8px rgba(0, 74, 173, 0.2);
                transform: translateY(-2px);
            }

            .nav-dropdown {
                width: 100%;
                margin: 0;
            }

            .nav-dropbtn {
                width: calc(100% - 2rem);
                justify-content: space-between;
                padding: 1rem 1.5rem;
                margin: 0.5rem 1rem;
                background: white;
                border: 1px solid #e0e4e8;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
                text-align: left;
                transition: all 0.3s ease;
            }

            .nav-dropbtn:hover {
                background: var(--primary-blue);
                color: white;
                border-color: var(--primary-blue);
                box-shadow: 0 4px 8px rgba(0, 74, 173, 0.2);
                transform: translateY(-2px);
            }

            .nav-dropdown-content {
                position: static;
                box-shadow: none;
                width: 100%;
                padding: 0;
                display: none;
                margin: 0;
                background: #f8f9fa;
            }

            .nav-dropdown-content a {
                padding: 0.8rem 1.5rem;
                margin: 0.3rem 1rem 0.3rem 2rem;
                font-size: 0.9rem;
                color: #666;
                background: white;
                border: 1px solid #e0e4e8;
                border-radius: 6px;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
                transition: all 0.3s ease;
                width: calc(100% - 3rem);
            }

            .nav-dropdown-content a:hover {
                background: var(--primary-pink);
                color: white;
                border-color: var(--primary-pink);
                box-shadow: 0 3px 6px rgba(205, 32, 139, 0.2);
                transform: translateY(-1px);
            }

            .nav-dropdown.active .nav-dropdown-content {
                display: block;
            }

            .right-section {
                display: flex;
                gap: 1rem;
            }

            .notification-dropdown {
                width: 280px;
                right: -80px;
            }

            .profile-dropdown-content {
                right: -40px;
                width: 200px;
            }

            .profile-button {
                width: 32px;
                height: 32px;
            }

            .profile-button img {
                width: 100%;
                height: 100%;
            }

            .profile-button:hover img {
                border-color: rgba(0, 74, 173, 0.3);
                transform: scale(1.05);
            }

            .notification-icon {
                width: 38px;
                height: 38px;
                display: flex;
                align-items: center;
                justify-content: center;
                background: rgba(255, 255, 255, 0.9);
                border: 1px solid rgba(0, 74, 173, 0.1);
                border-radius: 50%;
                transition: all 0.3s ease;
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
            }

            .notification-icon:hover {
                background: rgba(0, 74, 173, 0.05);
                border-color: rgba(0, 74, 173, 0.2);
                transform: scale(1.05);
            }

            .notification-icon i {
                font-size: 1.1rem;
                color: var(--primary-blue);
                font-weight: 500;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar">
        <div class="navbar-left">
            <a href="{{ url_for('landing_page') }}" style="text-decoration: none;">
                <div class="logo">
                    <img src="{{ url_for('static', filename='img/giggenius_logo.jpg') }}" alt="GigGenius Logo">
                    <h1>GigGenius</h1>
                </div>
            </a>
            <button class="mobile-menu-btn" id="mobileMenuBtn">
                <i class="fas fa-bars"></i>
            </button>
            <div class="nav-links" id="navLinks">
                <!-- Desktop Navigation Links -->
                <a href="{{ url_for('page1') }}">Post a Gig</a>

                <!-- Desktop Overview Dropdown -->
                <div class="nav-dropdown">
                    <button class="nav-dropbtn">Overview
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="nav-dropdown-content">
                        <a href="{{ url_for('allgigpost') }}" class="active">All gig posts</a>
                        <a href="{{ url_for('all_contracts') }}">All contracts</a>
                        <a href="{{ url_for('your_hires') }}">Your Hires</a>
                    </div>
                </div>

                <!-- Desktop Manage Work Dropdown -->
                <div class="nav-dropdown">
                    <button class="nav-dropbtn">Manage Work
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="nav-dropdown-content">
                        <a href="{{ url_for('landing_page') }}">Timesheet</a>
                        <a href="{{ url_for('landing_page') }}">Invoices</a>
                    </div>
                </div>

                <!-- Desktop Reports Dropdown -->
                <div class="nav-dropdown">
                    <button class="nav-dropbtn">Reports
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="nav-dropdown-content">
                        <a href="{{ url_for('landing_page') }}">Weekly Summary</a>
                        <a href="{{ url_for('landing_page') }}">Transaction History</a>
                    </div>
                </div>

                <a href="{{ url_for('messages') }}">Messages</a>
            </div>
        </div>
        <div class="right-section">
            <div class="auth-buttons">
                <div class="notification-icon">
                    <i class="fas fa-bell"></i>
                    <span id="notification-count">0</span>
                </div>
                <div class="notification-dropdown">
                    <div class="notification-header">
                        <span>Notifications</span>
                        <span class="notification-header-actions">Mark all as read</span>
                    </div>
                    <div id="notification-list">
                        <!-- Notifications will be loaded here -->
                    </div>
                    <div id="empty-notifications" class="empty-notifications" style="display: none;">
                        <i class="far fa-bell-slash"></i>
                        <p>No notifications yet</p>
                    </div>
                </div>
                <div class="profile-dropdown">
                    <div class="profile-button">
                        <img src="{{ url_for('api_profile_photo', user_type='client', user_id=session.get('user_id')) }}" alt="Profile Picture">
                    </div>
                    <div class="profile-dropdown-content">
                        <a href="{{ url_for('landing_page') }}">
                            <i class="fas fa-user"></i> My Profile
                        </a>
                        <a href="{{ url_for('landing_page') }}">
                            <i class="fas fa-cog"></i> Account Settings
                        </a>
                        <div class="dropdown-divider"></div>
                        <a href="{{ url_for('logout') }}" class="logout-option">
                            <i class="fas fa-sign-out-alt"></i> Log Out
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="dashboard">
        <div class="container">
            <h1>All Job Posts</h1>
            <button class="post-job-btn" onclick="window.location.href='{{ url_for('page1') }}'">
                <i class="fas fa-plus-circle"></i>
                Post a new job
            </button>
        </div>

        <div class="search-bar">
            <div class="search-container">
                <i class="fas fa-search"></i>
                <input type="text" placeholder="Search job postings by title or date..." id="searchInput">
            </div>
            <button class="filter-button" data-tooltip="Show/hide filters">
                <i class="fas fa-filter"></i>
                Filters
                <span class="filter-count" style="display: none;">0</span>
            </button>
        </div>

        <div class="filters">
            <div class="filter-group">
                <label><i class="fas fa-user-friends"></i> Posted by</label>
                <select class="filter-select">
                    <option>All coworkers</option>
                    <option>Me</option>
                    <option>My team</option>
                </select>
            </div>

            <div class="filter-group">
                <label><i class="fas fa-eye"></i> Visibility</label>
                <select class="filter-select">
                    <option>All</option>
                    <option>Invite-only</option>
                    <option>Public</option>
                </select>
            </div>

            <div class="filter-group">
                <label><i class="fas fa-tasks"></i> Status</label>
                <select class="filter-select">
                    <option>All</option>
                    <option>Drafts</option>
                    <option>Open</option>
                    <option>Filled</option>
                    <option>Closed</option>
                </select>
            </div>

            <div class="filter-group">
                <label><i class="fas fa-dollar-sign"></i> Type</label>
                <select class="filter-select">
                    <option>All</option>
                    <option>Fixed-price</option>
                    <option>Hourly</option>
                </select>
            </div>
        </div>

        <div class="actions">
            <div class="status-tabs">
                <div class="selected-filters">
                    <!-- Filter tags will be added here dynamically -->
                </div>
                <button class="clear-filters">Clear all filters</button>
            </div>
        </div>
   <div class="job-list">
    <!-- Job cards will be displayed here -->
    {% if job_posts|length > 0 %}
        {% for job in job_posts %}
        <div class="job-card">
            <!-- Job Header with Title and Status -->
            <div class="job-header">
                <div class="job-title" onclick="window.location.href='{{ url_for('job_details', job_id=job.id) }}';" style="cursor: pointer;">
                    {{ job.title }}
                </div>
                <div class="job-status">
                    {% if job.actual_status|lower == 'draft' %}
                        <span class="status-badge draft">
                            <i class="fas fa-edit"></i> Draft
                        </span>
                    {% elif job.actual_status|lower in ['publish', 'published', 'open'] %}
                        <span class="status-badge published">
                            <i class="fas fa-globe"></i> Published
                        </span>
                    {% elif job.status|lower == 'filled' %}
                        <span class="status-badge filled">
                            <i class="fas fa-check-circle"></i> Filled
                        </span>
                    {% elif job.actual_status|lower == 'closed' %}
                        <span class="status-badge closed">
                            <i class="fas fa-times-circle"></i> Closed
                        </span>
                    {% elif job.actual_status|lower == 'paused' %}
                        <span class="status-badge paused">
                            <i class="fas fa-pause-circle"></i> Paused
                        </span>
                    {% else %}
                        <span class="status-badge unknown">
                            <i class="fas fa-question-circle"></i> {{ job.status_label or 'Unknown' }}
                        </span>
                    {% endif %}
                </div>
            </div>

            <!-- Job Description -->
            {% if job.description %}
            <div class="job-description">
                {% set description = job.description %}
                {% if description|length > 150 %}
                    <p>
                        <span class="description-short">{{ description[:150] }}...</span>
                        <span class="description-full" style="display: none;">{{ description }}</span>
                        <button class="see-more-btn" onclick="toggleDescription(this)">See more</button>
                    </p>
                {% else %}
                    <p>{{ description }}</p>
                {% endif %}
            </div>
            {% endif %}

            <!-- User-Friendly Project Information -->
            <div class="project-info-user-friendly">

                <!-- Essential Job Details Section -->
                <div class="section-container">
                    <div class="section-header">
                        <i class="fas fa-info-circle"></i>
                        <h4>Job Details</h4>
                    </div>
                    <div class="details-grid">
                        <div class="detail-box">
                            <div class="detail-label"><i class="bi bi-currency-dollar"></i> Budget</div>
                            <div class="detail-value">{{ job.budget_display }}</div>
                            {% if job.budget_type %}
                            <div class="detail-note">{{ job.budget_type|title }} Rate</div>
                            {% endif %}
                        </div>

                        {% if job.category %}
                        <div class="detail-box">
                            <div class="detail-label"><i class="bi bi-tags"></i> Category</div>
                            <div class="detail-value">{{ job.category }}</div>
                            {% if job.specialty %}
                            <div class="detail-note">{{ job.specialty }}</div>
                            {% endif %}
                        </div>
                        {% endif %}

                        {% if job.project_size %}
                        <div class="detail-box">
                            <div class="detail-label"><i class="bi bi-bar-chart"></i> Project Size</div>
                            <div class="detail-value">{{ job.project_size }}</div>
                            {% if job.duration %}
                            <div class="detail-note">Duration: {{ job.duration }}</div>
                            {% endif %}
                        </div>
                        {% endif %}

                        {% if job.job_type %}
                        <div class="detail-box">
                            <div class="detail-label"><i class="bi bi-briefcase"></i> Job Type</div>
                            <div class="detail-value">{{ job.job_type }}</div>
                        </div>
                        {% endif %}


                    </div>
                </div>

                <!-- Client Information Section -->
                <div class="section-container">
                    <div class="section-header">
                        <i class="fas fa-user-tie"></i>
                        <h4>Client Information</h4>
                    </div>
                    <div class="client-info-grid">
                        <div class="client-detail">
                            <span class="client-label"><i class="bi bi-person-circle"></i> Client:</span>
                            <span class="client-value">{{ job.client_display_name }}</span>
                        </div>
                        {% if job.client_country %}
                        <div class="client-detail">
                            <span class="client-label"><i class="bi bi-globe"></i> Location:</span>
                            <span class="client-value">{{ job.client_country }}</span>
                        </div>
                        {% endif %}
                        <div class="client-detail">
                            <span class="client-label"><i class="bi bi-calendar-date"></i> Posted:</span>
                            <span class="client-value">{{ job.created_at }}</span>
                        </div>
                    </div>
                </div>

                <!-- Skills & Requirements Section -->
                <!-- Debug: Skills value = "{{ job.skills }}" -->
                {% if job.skills or job.requirements or job.technologies %}
                <div class="section-container">
                    <div class="section-header">
                        <i class="fas fa-tools"></i>
                        <h4>Skills & Requirements</h4>
                    </div>
                    <div class="skills-requirements-grid">
                        {% if job.skills %}
                        <div class="skills-section">
                            <div class="skills-header">
                                <i class="fas fa-star"></i>
                                <span class="skills-title">Required Skills</span>
                            </div>
                            <div class="skills-tags">
                                {% set skills_list = job.skills %}
                                {% if skills_list.startswith('[') and skills_list.endswith(']') %}
                                    {% set skills_array = skills_list[1:-1].replace('"', '').split(',') %}
                                    {% for skill in skills_array %}
                                        {% if skill.strip() %}
                                        <span class="skill-tag">{{ skill.strip() }}</span>
                                        {% endif %}
                                    {% endfor %}
                                {% else %}
                                    {% for skill in skills_list.split(',') %}
                                        {% if skill.strip() %}
                                        <span class="skill-tag">{{ skill.strip() }}</span>
                                        {% endif %}
                                    {% endfor %}
                                {% endif %}
                            </div>
                        </div>
                        {% endif %}

                        {% if job.technologies %}
                        <div class="skills-section">
                            <div class="skills-header">
                                <i class="fas fa-code"></i>
                                <span class="skills-title">Technologies</span>
                            </div>
                            <div class="skills-tags">
                                {% for tech in job.technologies.split(',') %}
                                <span class="tech-tag">{{ tech.strip() }}</span>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}

                        {% if job.requirements %}
                        <div class="requirements-section">
                            <div class="skills-header">
                                <i class="fas fa-list-check"></i>
                                <span class="skills-title">Requirements</span>
                            </div>
                            <div class="requirements-text">
                                {{ job.requirements[:150] }}{% if job.requirements|length > 150 %}...{% endif %}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                <!-- Additional Job Information Section -->
                {% if job.experience_level or job.availability or job.location or job.remote_option %}
                <div class="section-container">
                    <div class="section-header">
                        <i class="fas fa-info-circle"></i>
                        <h4>Additional Information</h4>
                    </div>
                    <div class="additional-info-grid">
                        {% if job.experience_level %}
                        <div class="info-detail">
                            <span class="info-label"><i class="bi bi-target"></i> Experience Level:</span>
                            <span class="info-value">{{ job.experience_level }}</span>
                        </div>
                        {% endif %}

                        {% if job.availability %}
                        <div class="info-detail">
                            <span class="info-label"><i class="bi bi-clock"></i> Availability:</span>
                            <span class="info-value">{{ job.availability }}</span>
                        </div>
                        {% endif %}

                        {% if job.location %}
                        <div class="info-detail">
                            <span class="info-label"><i class="bi bi-geo-alt"></i> Location:</span>
                            <span class="info-value">{{ job.location }}</span>
                        </div>
                        {% endif %}

                        {% if job.remote_option %}
                        <div class="info-detail">
                            <span class="info-label">🏠 Remote:</span>
                            <span class="info-value">{{ job.remote_option }}</span>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                <!-- Application Activity Section -->
                <div class="section-container">
                    <div class="section-header">
                        <i class="fas fa-chart-line"></i>
                        <h4>Application Activity</h4>
                    </div>
                    <div class="activity-grid">
                        <div class="activity-item">
                            <div class="activity-number">{{ job.proposals_count or 0 }}</div>
                            <div class="activity-label">Total Proposals</div>
                            {% if job.new_proposals_count and job.new_proposals_count > 0 %}
                            <div class="activity-badge">{{ job.new_proposals_count }} new</div>
                            {% endif %}
                        </div>

                        {% if job.status|lower == 'open' %}
                        <div class="activity-item">
                            <div class="activity-number">{{ job.messaged_count or 0 }}</div>
                            <div class="activity-label">Messaged</div>
                        </div>

                        <div class="activity-item">
                            <div class="activity-number">{{ job.hired_count or 0 }}</div>
                            <div class="activity-label">Hired</div>
                        </div>
                        {% endif %}
                    </div>
                </div>

            </div>

            <!-- Job Actions -->
            <div class="job-actions-clean">
                {% if session.get('user_type') == 'genius' %}
                    {% if job.status|lower == 'open' %}
                    <button class="apply-btn" onclick="applyToJob({{ job.id }})">
                        <i class="fas fa-paper-plane"></i> Apply Now
                    </button>
                    {% endif %}
                {% elif session.get('user_type') == 'client' %}
                    {% if job.is_owner %}
                    <button class="edit-job-btn-enhanced" onclick="window.location.href='{{ url_for('page1', edit_job_id=job.id) }}';">
                        <i class="fas fa-edit"></i> Edit Post
                    </button>
                    {% endif %}
                {% endif %}
            </div>
        </div>

        {% endfor %}
    {% else %}
        <div class="no-jobs-found">
            <i class="fas fa-clipboard-list"></i>
            <h3>No job posts found</h3>
            <p>You haven't created any job posts yet. Click the button below to post your first job.</p>
            <button class="post-job-btn" onclick="window.location.href='{{ url_for('page1') }}'">
                <i class="fas fa-plus-circle"></i>
                Post a new job
            </button>
        </div>
    {% endif %}
</div>

            <!-- Pagination -->
            <div class="pagination-container" {% if pagination.total == 0 %}style="display: none;"{% endif %}>
                <div class="pagination-info">
                    Showing <span id="showing-count">{{ pagination.start_item }}-{{ pagination.end_item }}</span> of <span id="total-count">{{ pagination.total }}</span> job posts
                </div>
                <div class="pagination-controls">
                    {% if pagination.has_prev %}
                    <a href="{{ url_for('allgigpost', page=pagination.prev_num) }}" class="pagination-button">
                        <i class="fas fa-chevron-left"></i> Previous
                    </a>
                    {% else %}
                    <button class="pagination-button" disabled>
                        <i class="fas fa-chevron-left"></i> Previous
                    </button>
                    {% endif %}

                    <span class="page-info">Page {{ pagination.page }} of {{ pagination.total_pages }}</span>

                    {% if pagination.has_next %}
                    <a href="{{ url_for('allgigpost', page=pagination.next_num) }}" class="pagination-button">
                        Next <i class="fas fa-chevron-right"></i>
                    </a>
                    {% else %}
                    <button class="pagination-button" disabled>
                        Next <i class="fas fa-chevron-right"></i>
                    </button>
                    {% endif %}
                </div>
            </div>

            <!-- No results found state (hidden by default) -->
            <div class="no-results-found" style="display: none;">
                <i class="fas fa-search"></i>
                <h3>No matching jobs found</h3>
                <p>Try adjusting your search or filter criteria to find what you're looking for.</p>
                <button class="clear-filters-btn" onclick="clearAllFilters()">Clear all filters</button>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const filterButton = document.querySelector('.filter-button');
            const filtersSection = document.querySelector('.filters');
            const actionsSection = document.querySelector('.actions');
            const selectedFiltersContainer = document.querySelector('.selected-filters');
            const clearFiltersBtn = document.querySelector('.clear-filters');
            const searchInput = document.getElementById('searchInput');
            let activeFilters = new Set();

            // Add sticky behavior to filters when scrolling
            const dashboard = document.querySelector('.dashboard');
            const filtersTop = filtersSection ? filtersSection.offsetTop : 0;

            window.addEventListener('scroll', function() {
                if (filtersSection.classList.contains('show')) {
                    if (window.pageYOffset > filtersTop) {
                        filtersSection.style.position = 'sticky';
                        filtersSection.style.top = '80px';
                        filtersSection.style.zIndex = '100';
                        filtersSection.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1)';
                    } else {
                        filtersSection.style.position = 'relative';
                        filtersSection.style.top = '0';
                        filtersSection.style.boxShadow = 'none';
                    }
                }
            });

            // Enhanced filter button animation with ripple effect
            filterButton.addEventListener('click', function(e) {
                // Create ripple effect
                const ripple = document.createElement('span');
                ripple.classList.add('ripple');
                this.appendChild(ripple);

                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                ripple.style.width = ripple.style.height = `${size}px`;
                ripple.style.left = `${e.clientX - rect.left - size/2}px`;
                ripple.style.top = `${e.clientY - rect.top - size/2}px`;

                ripple.classList.add('active');

                setTimeout(() => {
                    ripple.remove();
                }, 500);

                // Toggle filter section
                filtersSection.classList.toggle('show');
                actionsSection.classList.toggle('show');
                filterButton.classList.toggle('active');

                if (filterButton.classList.contains('active')) {
                    filterButton.style.background = 'var(--primary-blue)';
                    filterButton.style.color = 'white';
                    filterButton.style.borderColor = 'var(--primary-blue)';
                } else {
                    filterButton.style.background = '#ffffff';
                    filterButton.style.color = 'var(--primary-blue)';
                    filterButton.style.borderColor = 'var(--primary-blue)';
                }
            });

            // Function to filter job cards based on selection
            function filterJobCards(select) {
                const value = select.value;
                const jobCards = document.querySelectorAll('.job-card');

                jobCards.forEach(card => {
                    const hasReuseButton = card.querySelector('.reuse-posting-btn') !== null;
                    const hasEditButton = !hasReuseButton; // If no reuse button, assume it has edit button

                    let shouldShow = true;

                    // Show cards with reuse button
                    if (['Me', 'My team', 'Invite-only', 'Filled', 'Closed', 'Fixed-price'].includes(value)) {
                        shouldShow = hasReuseButton;
                    }
                    // Show cards with edit button
                    else if (['Public', 'Drafts', 'Open', 'Hourly'].includes(value)) {
                        shouldShow = hasEditButton;
                    }
                    // Show all cards
                    else if (['All coworkers', 'All'].includes(value)) {
                        shouldShow = true;
                    }

                    if (shouldShow) {
                        card.style.display = '';
                        card.style.animation = 'fadeIn 0.3s ease-in-out';
                    } else {
                        card.style.display = 'none';
                    }
                });

                updateJobCount();
            }

            // Enhanced select changes with animation
            document.querySelectorAll('.filter-select').forEach(select => {
                select.addEventListener('change', function() {
                    const label = this.parentElement.querySelector('label').textContent;
                    const value = this.value;

                    filterJobCards(this);

                    if (value !== 'All' && value !== 'All coworkers') {
                        addFilterTag(label, value);
                        activeFilters.add(label);
                    } else {
                        removeFilterByLabel(label);
                        activeFilters.delete(label);
                    }

                    updateClearFiltersVisibility();
                    animateFilterChange();
                });
            });

            // Search functionality with no results handling
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const jobCards = document.querySelectorAll('.job-card');
                const noResultsFound = document.querySelector('.no-results-found');
                let visibleCount = 0;

                jobCards.forEach(card => {
                    const title = card.querySelector('.job-title').textContent.toLowerCase();
                    const info = card.querySelector('.job-info').textContent.toLowerCase();

                    if (title.includes(searchTerm) || info.includes(searchTerm)) {
                        card.style.display = '';
                        card.style.animation = 'fadeIn 0.3s ease-in-out';
                        visibleCount++;
                    } else {
                        card.style.display = 'none';
                    }
                });

                // Show/hide no results message
                if (visibleCount === 0 && searchTerm.length > 0) {
                    noResultsFound.style.display = 'flex';
                    document.querySelector('.pagination-container').style.display = 'none';
                } else {
                    noResultsFound.style.display = 'none';
                    document.querySelector('.pagination-container').style.display = 'flex';
                }

                updateJobCount();
            });

            function addFilterTag(label, value) {
                removeFilterByLabel(label);

                const tag = document.createElement('div');
                tag.className = 'filter-tag';
                tag.dataset.label = label;
                tag.innerHTML = `
                    ${label}: ${value}
                    <span class="remove-tag" onclick="removeFilterTag(this)">
                        <i class="fas fa-times"></i>
                    </span>
                `;

                // Add entrance animation
                tag.style.opacity = '0';
                tag.style.transform = 'translateY(-10px)';
                selectedFiltersContainer.appendChild(tag);

                // Trigger animation
                setTimeout(() => {
                    tag.style.opacity = '1';
                    tag.style.transform = 'translateY(0)';
                }, 50);
            }

            function removeFilterByLabel(label) {
                const existingTag = Array.from(selectedFiltersContainer.children)
                    .find(tag => tag.dataset.label === label);

                if (existingTag) {
                    // Add exit animation
                    existingTag.style.opacity = '0';
                    existingTag.style.transform = 'translateY(-10px)';
                    setTimeout(() => existingTag.remove(), 300);
                }
            }

            function animateFilterChange() {
                const jobCards = document.querySelectorAll('.job-card');
                jobCards.forEach(card => {
                    if (card.style.display !== 'none') {
                        card.style.transition = 'all 0.3s ease';
                        card.style.transform = 'scale(0.98)';
                        card.style.opacity = '0.8';

                        setTimeout(() => {
                            card.style.transform = 'scale(1)';
                            card.style.opacity = '1';
                        }, 300);
                    }
                });
            }

            function updateJobCount() {
                const visibleCards = Array.from(document.querySelectorAll('.job-card')).filter(card =>
                    card.style.display !== 'none'
                ).length;

                const totalCards = document.querySelectorAll('.job-card').length;
                const showingCountEl = document.getElementById('showing-count');
                const totalCountEl = document.getElementById('total-count');
                const paginationContainer = document.querySelector('.pagination-container');
                const noResultsFound = document.querySelector('.no-results-found');

                if (showingCountEl && totalCountEl) {
                    showingCountEl.textContent = visibleCards > 0 ? `1-${visibleCards}` : '0';
                    totalCountEl.textContent = totalCards;
                }

                // Show/hide pagination and no results message
                if (visibleCards === 0 && totalCards > 0) {
                    paginationContainer.style.display = 'none';
                    noResultsFound.style.display = 'flex';
                } else if (visibleCards > 0) {
                    paginationContainer.style.display = 'flex';
                    noResultsFound.style.display = 'none';
                }
            }

            // Enhanced clear filters with animation
            clearFiltersBtn.addEventListener('click', function() {
                const tags = Array.from(selectedFiltersContainer.children);

                // Animate all tags removal
                tags.forEach((tag, index) => {
                    setTimeout(() => {
                        tag.style.opacity = '0';
                        tag.style.transform = 'translateY(-10px)';
                    }, index * 100);
                });

                // Reset selects and clear container
                setTimeout(() => {
                    document.querySelectorAll('select').forEach(select => {
                        select.selectedIndex = 0;
                    });
                    selectedFiltersContainer.innerHTML = '';
                    activeFilters.clear();
                    updateClearFiltersVisibility();
                    animateFilterChange();

                    // Reset search
                    searchInput.value = '';
                    const event = new Event('input');
                    searchInput.dispatchEvent(event);
                }, tags.length * 100);
            });

            // Function to update clear filters button visibility
            function updateClearFiltersVisibility() {
                const filterCount = selectedFiltersContainer.children.length;
                const filterCountBadge = document.querySelector('.filter-count');

                if (filterCount > 0) {
                    clearFiltersBtn.classList.add('show');

                    // Update filter count badge
                    if (filterCountBadge) {
                        filterCountBadge.textContent = filterCount;
                        filterCountBadge.style.display = 'flex';
                    }
                } else {
                    clearFiltersBtn.classList.remove('show');

                    // Hide filter count badge
                    if (filterCountBadge) {
                        filterCountBadge.style.display = 'none';
                    }
                }
            }

            // Initialize job count
            updateJobCount();
        });

        // Global function to remove filter tags
        function removeFilterTag(element) {
            const tag = element.closest('.filter-tag');
            const label = tag.dataset.label;

            // Reset corresponding select element
            document.querySelectorAll('.filter-group').forEach(group => {
                if (group.querySelector('label').textContent === label) {
                    group.querySelector('select').selectedIndex = 0;

                    // Trigger change event
                    const event = new Event('change');
                    group.querySelector('select').dispatchEvent(event);
                }
            });

            tag.remove();

            // Update clear filters button visibility
            const clearFiltersBtn = document.querySelector('.clear-filters');
            const selectedFiltersContainer = document.querySelector('.selected-filters');
            if (selectedFiltersContainer.children.length === 0) {
                clearFiltersBtn.classList.remove('show');
            }
        }

        // Draft management functions
        function publishDraft(jobId) {
            if (confirm('Are you sure you want to publish this draft? Once published, it will be visible to all freelancers.')) {
                // Send AJAX request to publish the draft
                fetch(`/publish_draft/${jobId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCsrfToken()
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Show success message and reload page
                        alert('Draft published successfully!');
                        window.location.reload();
                    } else {
                        alert('Error publishing draft: ' + (data.message || 'Unknown error'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error publishing draft. Please try again.');
                });
            }
        }

        function deleteDraft(jobId) {
            if (confirm('Are you sure you want to delete this draft? This action cannot be undone.')) {
                // Send AJAX request to delete the draft
                fetch(`/delete_draft/${jobId}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCsrfToken()
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Remove the card with animation
                        const draftCard = document.querySelector(`[data-job-id="${jobId}"]`).closest('.job-card');
                        if (draftCard) {
                            draftCard.style.opacity = '0';
                            draftCard.style.transform = 'translateY(-20px)';
                            setTimeout(() => {
                                draftCard.remove();
                                updateJobCount();
                            }, 300);
                        }
                        alert('Draft deleted successfully!');
                    } else {
                        alert('Error deleting draft: ' + (data.message || 'Unknown error'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error deleting draft. Please try again.');
                });
            }
        }

        function getCsrfToken() {
            // Get CSRF token from meta tag or cookie
            const token = document.querySelector('meta[name="csrf-token"]');
            return token ? token.getAttribute('content') : '';
        }

        // Job application function for geniuses
        function applyToJob(jobId) {
            if (confirm('Are you sure you want to apply to this job?')) {
                // Send AJAX request to apply to the job
                fetch(`/apply_to_job/${jobId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCsrfToken()
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Application submitted successfully!');
                        // Update the button to show applied state
                        const applyBtn = document.querySelector(`button[onclick="applyToJob(${jobId})"]`);
                        if (applyBtn) {
                            applyBtn.innerHTML = '<i class="fas fa-check"></i> Applied';
                            applyBtn.disabled = true;
                            applyBtn.style.background = '#10b981';
                        }
                    } else {
                        alert('Error applying to job: ' + (data.message || 'Unknown error'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error applying to job. Please try again.');
                });
            }
        }

        // Bookmark job function
        function bookmarkJob(jobId) {
            const bookmarkBtn = document.querySelector(`button[onclick="bookmarkJob(${jobId})"]`);
            const isBookmarked = bookmarkBtn.classList.contains('bookmarked');

            // Send AJAX request to bookmark/unbookmark the job
            fetch(`/bookmark_job/${jobId}`, {
                method: isBookmarked ? 'DELETE' : 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCsrfToken()
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (isBookmarked) {
                        bookmarkBtn.classList.remove('bookmarked');
                        bookmarkBtn.innerHTML = '<i class="far fa-bookmark"></i>';
                        bookmarkBtn.setAttribute('data-tooltip', 'Save this job');
                    } else {
                        bookmarkBtn.classList.add('bookmarked');
                        bookmarkBtn.innerHTML = '<i class="fas fa-bookmark"></i>';
                        bookmarkBtn.setAttribute('data-tooltip', 'Remove from saved');
                    }
                } else {
                    alert('Error bookmarking job: ' + (data.message || 'Unknown error'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error bookmarking job. Please try again.');
            });
        }

        // Function to clear all filters and search
        function clearAllFilters() {
            // Clear search input
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.value = '';
                const inputEvent = new Event('input');
                searchInput.dispatchEvent(inputEvent);
            }

            // Reset all select elements
            document.querySelectorAll('.filter-select').forEach(select => {
                select.selectedIndex = 0;
                const changeEvent = new Event('change');
                select.dispatchEvent(changeEvent);
            });

            // Clear filter tags
            const selectedFiltersContainer = document.querySelector('.selected-filters');
            if (selectedFiltersContainer) {
                selectedFiltersContainer.innerHTML = '';

                // Update filter count badge
                const filterCountBadge = document.querySelector('.filter-count');
                if (filterCountBadge) {
                    filterCountBadge.style.display = 'none';
                }
            }

            // Hide no results message
            const noResultsFound = document.querySelector('.no-results-found');
            if (noResultsFound) {
                noResultsFound.style.display = 'none';
            }

            // Show pagination
            const paginationContainer = document.querySelector('.pagination-container');
            if (paginationContainer) {
                paginationContainer.style.display = 'flex';
            }

            // Show all job cards with animation
            const jobCards = document.querySelectorAll('.job-card');
            jobCards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.display = '';
                    card.style.animation = 'fadeIn 0.3s ease-out';
                }, index * 50); // Staggered animation
            });

            // Update job count
            setTimeout(updateJobCount, 100);
        }

        // Profile dropdown functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Add dropdown divider styling
            const dropdownDividers = document.querySelectorAll('.dropdown-divider');
            dropdownDividers.forEach(divider => {
                divider.style.borderTop = '1px solid #e9ecef';
                divider.style.margin = '0.5rem 0';
            });

            // Mobile menu toggle functionality
            const mobileMenuBtn = document.getElementById('mobileMenuBtn');
            const navLinks = document.getElementById('navLinks');

            function openMobileMenu() {
                navLinks.classList.add('active');
                document.body.style.overflow = 'hidden'; // Prevent background scrolling

                // Change icon to close
                const icon = mobileMenuBtn.querySelector('i');
                icon.classList.remove('fa-bars');
                icon.classList.add('fa-times');
            }

            function closeMobileMenu() {
                navLinks.classList.remove('active');
                document.body.style.overflow = ''; // Restore background scrolling

                // Change icon back to bars
                const icon = mobileMenuBtn.querySelector('i');
                icon.classList.remove('fa-times');
                icon.classList.add('fa-bars');
            }

            if (mobileMenuBtn) {
                mobileMenuBtn.addEventListener('click', function() {
                    if (navLinks.classList.contains('active')) {
                        closeMobileMenu();
                    } else {
                        openMobileMenu();
                    }
                });
            }

            // Mobile dropdown toggles
            const navDropdowns = document.querySelectorAll('.nav-dropdown');

            navDropdowns.forEach(dropdown => {
                const dropBtn = dropdown.querySelector('.nav-dropbtn');

                dropBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    // Toggle current dropdown
                    dropdown.classList.toggle('active');

                    // Close other dropdowns
                    navDropdowns.forEach(otherDropdown => {
                        if (otherDropdown !== dropdown) {
                            otherDropdown.classList.remove('active');
                            const otherIcon = otherDropdown.querySelector('.nav-dropbtn i');
                            if (otherIcon) {
                                otherIcon.classList.remove('fa-chevron-up');
                                otherIcon.classList.add('fa-chevron-down');
                            }
                        }
                    });

                    // Change dropdown icon
                    const icon = this.querySelector('i');
                    if (dropdown.classList.contains('active')) {
                        icon.classList.remove('fa-chevron-down');
                        icon.classList.add('fa-chevron-up');
                    } else {
                        icon.classList.remove('fa-chevron-up');
                        icon.classList.add('fa-chevron-down');
                    }
                });
            });

            // Close mobile menu when clicking outside
            document.addEventListener('click', function(e) {
                if (window.innerWidth <= 768 &&
                    !navLinks.contains(e.target) &&
                    !mobileMenuBtn.contains(e.target) &&
                    navLinks.classList.contains('active')) {
                    closeMobileMenu();
                }
            });

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth > 768 && navLinks.classList.contains('active')) {
                    closeMobileMenu();

                    // Reset all dropdowns
                    navDropdowns.forEach(dropdown => {
                        dropdown.classList.remove('active');
                        const icon = dropdown.querySelector('.nav-dropbtn i');
                        if (icon) {
                            icon.classList.remove('fa-chevron-up');
                            icon.classList.add('fa-chevron-down');
                        }
                    });
                }
            });

            // Close navigation dropdowns when clicking outside
            document.addEventListener('click', function(e) {
                // Check if click is outside all nav dropdowns
                let clickedInsideDropdown = false;

                navDropdowns.forEach(dropdown => {
                    if (dropdown.contains(e.target)) {
                        clickedInsideDropdown = true;
                    }
                });

                // If clicked outside all dropdowns, close them all
                if (!clickedInsideDropdown) {
                    navDropdowns.forEach(dropdown => {
                        dropdown.classList.remove('active');
                        const icon = dropdown.querySelector('.nav-dropbtn i');
                        if (icon) {
                            icon.classList.remove('fa-chevron-up');
                            icon.classList.add('fa-chevron-down');
                        }
                    });
                }
            });

            // Simple notification dropdown toggle
            const notificationIcon = document.querySelector('.notification-icon');
            const notificationDropdown = document.querySelector('.notification-dropdown');

            if (notificationIcon) {
                notificationIcon.addEventListener('click', function(e) {
                    e.stopPropagation();
                    notificationDropdown.style.display = notificationDropdown.style.display === 'block' ? 'none' : 'block';

                    // Close profile dropdown if open
                    const profileDropdown = document.querySelector('.profile-dropdown');
                    if (profileDropdown) {
                        profileDropdown.classList.remove('active');
                    }
                });
            }

            // Profile dropdown functionality
            const profileDropdown = document.querySelector('.profile-dropdown');
            const profileButton = profileDropdown.querySelector('.profile-button');

            // Toggle dropdown on profile button click
            profileButton.addEventListener('click', function(e) {
                e.stopPropagation();
                profileDropdown.classList.toggle('active');

                // Close notification dropdown if open
                if (notificationDropdown) {
                    notificationDropdown.style.display = 'none';
                }
            });

            // Close dropdowns when clicking outside
            document.addEventListener('click', function(e) {
                if (!profileDropdown.contains(e.target)) {
                    profileDropdown.classList.remove('active');
                }
                if (!notificationIcon.contains(e.target)) {
                    notificationDropdown.style.display = 'none';
                }
            });

            // Mark all notifications as read
            const markAllBtn = document.querySelector('.notification-header-actions');
            if (markAllBtn) {
                markAllBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    // Reset notification count
                    const notificationCount = document.getElementById('notification-count');
                    if (notificationCount) {
                        notificationCount.textContent = '0';
                    }

                    // Here you can add AJAX call to mark notifications as read on server
                    console.log('Marking all notifications as read');
                });
            }
        });
    </script>

    <script>
        function toggleDescription(button) {
            const shortText = button.parentElement.querySelector('.description-short');
            const fullText = button.parentElement.querySelector('.description-full');

            if (fullText.style.display === 'none') {
                // Show full text
                shortText.style.display = 'none';
                fullText.style.display = 'inline';
                button.textContent = 'See less';
            } else {
                // Show short text
                shortText.style.display = 'inline';
                fullText.style.display = 'none';
                button.textContent = 'See more';
            }
        }
    </script>
</body>
</html>