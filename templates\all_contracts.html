<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>All Contracts - GigGenius</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-blue: #004AAD;
            --primary-pink: #CD208B;
            --neutral-900: #1f2937;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
            color: #333;
            margin: 0;
            padding: 0;
        }

        .container {
            max-width: 2000px;
            margin: 0;
            padding: 0;
        }

        /* Navbar Styles */
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            height: 4.5rem;
            position: relative;
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }

        /* Mobile menu button */
        .mobile-menu-btn {
            display: none; /* Hidden by default (desktop view) */
            background: none;
            border: none;
            color: var(--primary-blue);
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0.5rem;
            margin-right: 0.5rem;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .mobile-menu-btn:hover {
            color: var(--primary-pink);
            background-color: rgba(0, 74, 173, 0.05);
        }

        .navbar-left {
            display: flex;
            align-items: center;
            gap: 2rem;
            padding-left: 0;
            flex: 1;
        }

        .logo {
            display: flex;
            align-items: center;
            color: var(--primary-pink);
        }

        .logo img {
            width: 3.5rem;
            height: 3.5rem;
        }

        .logo h1 {
            font-size: 1.5rem;
            font-weight: bold;
            margin-left: 0.5rem;
            margin-right: 0.5rem;
            color: var(--primary-pink);
        }

        .logo:hover, .logo:active {
            color: var(--primary-blue);
        }

        .logo:hover h1 {
            color: var(--primary-blue);
        }

        .nav-links {
            display: flex;
            gap: 1rem;
            align-items: center;
            height: 100%;
            margin: 0;
        }

        .nav-links a {
            color: var(--primary-blue);
            text-decoration: none;
            padding: 0.5rem 1rem;
            font-size: 1rem;
            font-weight: 500;
            position: relative;
            white-space: nowrap;
        }

        .nav-links a:hover, .nav-links a.active {
            color: var(--primary-pink);
        }

        .nav-links a:after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: 0;
            left: 0;
            background-color: var(--primary-pink);
            transition: width 0.3s ease;
        }

        .nav-links a:hover:after, .nav-links a.active:after {
            width: 100%;
        }

        .nav-dropdown {
            position: relative;
            display: inline-block;
            margin: 0;
        }

        .nav-dropbtn {
            font-weight: 500;
            font-size: 1rem;
            color: var(--primary-blue);
            background: none;
            border: none;
            padding: 0.5rem 1rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.3rem;
            position: relative;
            white-space: nowrap;
        }

        .nav-dropbtn:hover {
            color: var(--primary-pink);
        }

        .nav-dropbtn:after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: 0;
            left: 0;
            background-color: var(--primary-pink);
            transition: width 0.3s ease;
        }

        .nav-dropbtn:hover:after {
            width: 100%;
        }

        .nav-dropdown-content {
            display: none;
            position: absolute;
            background-color: #fff;
            min-width: 200px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            border-radius: 8px;
            z-index: 1001;
            top: 100%;
            left: 0;
            margin-top: 0.5rem;
        }

        .nav-dropdown-content a {
            color: var(--primary-blue);
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            font-size: 0.9rem;
        }

        .nav-dropdown-content a:hover {
            background-color: #f9f9f9;
            color: var(--primary-pink);
        }

        .nav-dropdown-content a:after {
            display: none;
        }

        .nav-dropdown.active .nav-dropdown-content {
            display: block;
        }

        /* Right section container */
        .right-section {
            display: flex;
            align-items: center;
            gap: 2rem;
            padding-right: 0;
            height: 100%;
        }

        /* Search container */
        .search-container {
            display: flex;
            align-items: center;
            margin-right: 1.5rem;
        }

        @media (max-width: 992px) {
            .right-section {
                gap: 1rem;
            }

            .search-container {
                margin-right: 1rem;
            }
        }

        .search-bar {
            height: 40px;
            display: flex;
            align-items: center;
            background: #f5f7fa;
            border: 1px solid #e1e8ed;
            border-radius: 20px;
            width: 280px;
            transition: all 0.3s ease;
            position: relative;
            box-shadow: 0 2px 8px rgba(0, 74, 173, 0.05);
            padding: 0 4px 0 6px;
        }

        .search-dropdown {
            position: relative;
            margin-right: 12px;
            border-right: 1px solid #e1e8ed;
            padding-right: 12px;
        }

        .search-dropdown-btn {
            background: white;
            border: 1px solid #d1dce5;
            border-radius: 15px;
            padding: 4px 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            font-weight: 500;
            color: #2c3e50;
            white-space: nowrap;
            height: 28px;
            transition: all 0.2s ease;
        }

        .search-dropdown-btn:hover {
            background: #f8f9fa;
            border-color: var(--primary-blue);
        }

        .search-dropdown-btn i {
            font-size: 10px;
            transition: transform 0.3s ease;
            color: #6c757d;
        }

        .search-dropdown.active .search-dropdown-btn i {
            transform: rotate(180deg);
        }

        .search-dropdown-menu {
            position: absolute;
            top: calc(100% + 5px);
            left: 0;
            background: white;
            border: 1px solid #e1e8ed;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 74, 173, 0.15);
            z-index: 9999;
            min-width: 160px;
            display: none;
            overflow: hidden;
        }

        .search-dropdown-menu.show {
            display: block;
            animation: dropdownFadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @keyframes dropdownFadeIn {
            from {
                opacity: 0;
                transform: translateY(-8px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .search-dropdown-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 13px;
            font-weight: 500;
            color: #2c3e50;
            border-bottom: 1px solid #f1f5f9;
        }

        .search-dropdown-item:last-child {
            border-bottom: none;
        }

        .search-dropdown-item:hover {
            background: #f8f9fa;
            color: var(--primary-blue);
        }

        .search-dropdown-item i {
            width: 14px;
            height: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-blue);
            font-size: 12px;
        }

        .search-bar:focus-within {
            background: white;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 3px rgba(0, 74, 173, 0.1);
        }

        @media (max-width: 768px) {
            .search-bar {
                width: 180px;
            }
        }

        @media (max-width: 576px) {
            .search-bar {
                width: 150px;
            }
        }

        .search-bar input {
            border: none;
            outline: none;
            padding: 0 0.5rem 0 1rem;
            width: 100%;
            height: 100%;
            font-size: 0.9rem;
            background: transparent;
        }

        .search-bar .icon {
            color: #8a94a6;
            padding: 0 1rem 0 0.5rem;
            font-size: 0.9rem;
        }

        .search-bar:focus-within .icon {
            color: var(--primary-blue);
        }

        /* Auth buttons container */
        .auth-buttons {
            display: flex;
            align-items: center;
            gap: 1.5rem; /* Increased gap between notification and profile */
        }

        /* Enhanced Notification Styles */
        .notification-icon {
            position: relative;
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .notification-icon:hover {
            background-color: rgba(0, 74, 173, 0.1);
        }

        .notification-icon i {
            font-size: 1.3rem;
            color: #4a5568;
        }

        #notification-count {
            position: absolute;
            top: 2px;
            right: 2px;
            background-color: var(--primary-pink);
            color: white;
            border-radius: 50%;
            min-width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
            font-weight: 600;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .notification-dropdown {
            position: absolute;
            top: 60px;
            right: 10px;
            width: 380px;
            max-height: 500px;
            overflow-y: auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
            z-index: 1000;
            display: none;
            border: 1px solid rgba(0,0,0,0.08);
            animation: dropdown-fade 0.2s ease-out;
        }

        @media (max-width: 576px) {
            .notification-dropdown {
                width: calc(100vw - 40px);
                right: -100px;
                max-height: 400px;
            }
        }

        @media (max-width: 480px) {
            .notification-dropdown {
                right: -150px;
            }
        }

        @keyframes dropdown-fade {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .notification-dropdown.show {
            display: block;
        }

        .notification-header {
            padding: 18px 20px;
            border-bottom: 1px solid rgba(0,0,0,0.08);
            font-weight: 600;
            font-size: 1rem;
            color: var(--neutral-900);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .notification-header-actions {
            font-size: 0.8rem;
            color: var(--primary-blue);
            cursor: pointer;
        }

        /* Profile dropdown */
        .profile-dropdown {
            position: relative;
            display: inline-block;
        }

        .profile-button {
            cursor: pointer;
            border-radius: 50%;
            overflow: hidden;
            width: 40px;
            height: 40px;
            border: 2px solid rgba(0, 74, 173, 0.1);
            transition: border-color 0.3s ease;
        }

        .profile-button:hover {
            border-color: var(--primary-blue);
        }

        .profile-button img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .profile-dropdown-content {
            display: none;
            position: absolute;
            right: 0;
            top: 60px;
            background-color: #fff;
            min-width: 220px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            border-radius: 8px;
            z-index: 1001;
            border: 1px solid rgba(0,0,0,0.1);
        }

        .profile-dropdown-content a {
            color: var(--primary-blue);
            padding: 12px 16px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .profile-dropdown-content a i {
            width: 20px;
            text-align: center;
        }

        .profile-dropdown-content a:hover {
            background-color: #f9f9f9;
            color: var(--primary-pink);
        }

        .dropdown-divider {
            height: 1px;
            background-color: #eee;
            margin: 8px 0;
        }

        .logout-option {
            color: #dc3545 !important;
        }

        .logout-option:hover {
            background-color: #fff5f5 !important;
            color: #dc3545 !important;
        }

        /* Show dropdown on click */
        .profile-dropdown.active .profile-dropdown-content {
            display: block;
        }

        /* Mobile Styles */
        .mobile-menu-header,
        .mobile-menu-content,
        .mobile-profile-section,
        .mobile-search-section,
        .mobile-profile-actions {
            display: none;
        }

        @media (max-width: 768px) {
            .navbar {
                padding: 0 0.5rem;
                height: 3.8rem;
            }

            .navbar-left {
                padding-left: 2.2rem;
            }

            .nav-links {
                display: none;
            }

            .mobile-menu-btn {
                display: flex;
                position: absolute;
                left: 0.4rem;
                top: 50%;
                transform: translateY(-50%);
                z-index: 1001;
            }

            .nav-links.active {
                display: flex;
                flex-direction: column;
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
                box-shadow: 0 0 50px rgba(0, 0, 0, 0.1);
                z-index: 9999;
                padding: 0;
                margin: 0;
                overflow-y: auto;
                -webkit-overflow-scrolling: touch;
                scroll-behavior: smooth;
            }

            .mobile-menu-header,
            .mobile-menu-content,
            .mobile-profile-section,
            .mobile-search-section,
            .mobile-profile-actions {
                display: block;
            }

            .right-section {
                display: none !important;
            }
        }

        .page-header {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .page-header h1 {
            color: #2d3748;
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .page-header p {
            color: #6b7280;
            font-size: 16px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 0 20px 30px 20px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-card .icon {
            width: 50px;
            height: 50px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            font-size: 20px;
            color: white;
        }

        .stat-card.active .icon {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }

        .stat-card.completed .icon {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        }

        .stat-card.pending .icon {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }

        .stat-card.cancelled .icon {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        }

        .stat-card h3 {
            font-size: 28px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 5px;
        }

        .stat-card p {
            color: #6b7280;
            font-size: 14px;
            font-weight: 500;
        }

        .filters-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin: 0 20px 30px 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
        }

        .filter-group label {
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 5px;
        }

        .filter-select, .filter-input {
            padding: 10px 12px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .filter-select:focus, .filter-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .search-btn {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-pink) 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .search-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .contracts-section {
            background: white;
            border-radius: 12px;
            padding: 25px;
            margin: 0 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 1px solid #f3f4f6;
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #1f2937;
        }

        .contract-card {
            border: 1px solid #e5e7eb;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }

        .contract-card:hover {
            border-color: #667eea;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
        }

        .contract-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }

        .contract-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 5px;
        }

        .contract-id {
            font-size: 12px;
            color: #6b7280;
            font-weight: 500;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-badge.active {
            background: #d1fae5;
            color: #065f46;
        }

        .status-badge.completed {
            background: #dbeafe;
            color: #1e40af;
        }

        .status-badge.pending {
            background: #fef3c7;
            color: #92400e;
        }

        .status-badge.cancelled {
            background: #fee2e2;
            color: #991b1b;
        }

        .contract-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }

        .detail-item {
            display: flex;
            flex-direction: column;
        }

        .detail-label {
            font-size: 12px;
            color: #6b7280;
            font-weight: 500;
            margin-bottom: 3px;
        }

        .detail-value {
            font-size: 14px;
            color: #1f2937;
            font-weight: 600;
        }

        .contract-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .action-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-pink) 100%);
            color: white;
        }

        .btn-secondary {
            background: #f3f4f6;
            color: #374151;
            border: 1px solid #d1d5db;
        }

        .btn-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
        }

        .action-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
        }

        .no-contracts {
            text-align: center;
            padding: 60px 20px;
            color: #6b7280;
        }

        .no-contracts i {
            font-size: 48px;
            margin-bottom: 20px;
            color: #d1d5db;
        }

        .no-contracts h3 {
            font-size: 20px;
            margin-bottom: 10px;
            color: #374151;
        }

        @media (max-width: 768px) {
            .page-header {
                margin: 10px;
                padding: 20px;
            }

            .page-header h1 {
                font-size: 24px;
            }

            .stats-grid {
                margin: 0 10px 20px 10px;
            }

            .filters-section {
                margin: 0 10px 20px 10px;
            }

            .contracts-section {
                margin: 0 10px;
            }

            .filters-grid {
                grid-template-columns: 1fr;
            }

            .contract-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .contract-details {
                grid-template-columns: 1fr;
            }

            .contract-actions {
                justify-content: flex-start;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Navbar -->
        <nav class="navbar">
            <div class="navbar-left">
                <a href="{{ url_for('landing_page') }}" style="text-decoration: none;">
                    <div class="logo">
                        <img src="{{ url_for('static', filename='img/giggenius_logo.jpg') }}" alt="GigGenius Logo">
                        <h1>GigGenius</h1>
                    </div>
                </a>
                <button class="mobile-menu-btn" id="mobileMenuBtn">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="nav-links" id="navLinks">
                    <!-- Mobile Menu Header (only visible on mobile) -->
                    <div class="mobile-menu-header">
                        <div class="logo">
                            <img src="{{ url_for('static', filename='img/giggenius_logo.jpg') }}" alt="GigGenius Logo">
                            <h1>GigGenius</h1>
                        </div>
                        <button class="mobile-close-btn" id="mobileCloseBtn">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <!-- Mobile Menu Content Wrapper -->
                    <div class="mobile-menu-content">
                        <!-- Mobile Profile Section (only visible on mobile) - At Top -->
                        <div class="mobile-profile-section">
                            <div class="mobile-profile-info">
                                <img src="{{ url_for('api_profile_photo', user_type='client', user_id=session.get('user_id')) }}" alt="Profile Picture" class="mobile-profile-pic">
                                <div class="mobile-profile-details">
                                    <h3>{{ session.first_name }} {{ session.last_name }}</h3>
                                    <p>{{ session.email }}</p>
                                </div>
                            </div>
                        </div>

                        <!-- Mobile Search Section (only visible on mobile) -->
                        <div class="mobile-search-section">
                            <div class="mobile-search-container">
                                <div class="mobile-search-bar">
                                    <div class="search-dropdown">
                                        <button class="search-dropdown-btn" onclick="toggleMobileSearchDropdown()">
                                            <span id="mobileSearchDropdownText">Geniuses</span>
                                            <i class="fas fa-chevron-down"></i>
                                        </button>
                                        <div class="search-dropdown-menu" id="mobileSearchDropdownMenu">
                                            <div class="search-dropdown-item" onclick="selectMobileSearchType('Geniuses')">
                                                <i class="fas fa-user-graduate"></i>
                                                <span>Geniuses</span>
                                            </div>
                                            <div class="search-dropdown-item" onclick="selectMobileSearchType('Jobs')">
                                                <i class="fas fa-briefcase"></i>
                                                <span>Jobs</span>
                                            </div>
                                            <div class="search-dropdown-item" onclick="selectMobileSearchType('Projects')">
                                                <i class="fas fa-project-diagram"></i>
                                                <span>Projects</span>
                                            </div>
                                        </div>
                                    </div>
                                    <input type="text" id="mobileSearchInput" placeholder="Search for Geniuses">
                                    <i class="fas fa-search icon"></i>
                                </div>
                            </div>
                        </div>
                    </div> <!-- End mobile-menu-content -->

                    <!-- Desktop Navigation Links (visible on desktop) -->
                    <a href="{{ url_for('page1') }}">Post a Gig</a>

                    <!-- Desktop Overview Dropdown -->
                    <div class="nav-dropdown">
                        <button class="nav-dropbtn">Overview
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="nav-dropdown-content">
                            <a href="{{ url_for('allgigpost') }}">All gig posts</a>
                            <a href="{{ url_for('all_contracts') }}">All contracts</a>
                            <a href="{{ url_for('landing_page') }}">Your Hires</a>
                        </div>
                    </div>

                    <!-- Desktop Manage Work Dropdown -->
                    <div class="nav-dropdown">
                        <button class="nav-dropbtn">Manage Work
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="nav-dropdown-content">
                            <a href="{{ url_for('landing_page') }}">Timesheet</a>
                            <a href="{{ url_for('landing_page') }}">Invoices</a>
                        </div>
                    </div>

                    <!-- Desktop Reports Dropdown -->
                    <div class="nav-dropdown">
                        <button class="nav-dropbtn">Reports
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="nav-dropdown-content">
                            <a href="{{ url_for('landing_page') }}">Weekly Summary</a>
                            <a href="{{ url_for('landing_page') }}">Transaction History</a>
                        </div>
                    </div>

                    <a href="{{ url_for('messages') }}">Messages</a>

                    <!-- Mobile Profile Actions (only visible on mobile) - At Bottom -->
                    <div class="mobile-profile-actions">
                        <a href="{{ url_for('landing_page') }}" class="mobile-profile-link">
                            <i class="fas fa-user"></i> My Profile
                        </a>
                        <a href="{{ url_for('landing_page') }}" class="mobile-profile-link">
                            <i class="fas fa-cog"></i> Account Settings
                        </a>
                        <a href="{{ url_for('logout') }}" class="mobile-profile-link logout">
                            <i class="fas fa-sign-out-alt"></i> Log Out
                        </a>
                    </div>
                </div>
            </div>
            <div class="right-section">
                <div class="search-container">
                    <div class="search-bar">
                        <div class="search-dropdown">
                            <button class="search-dropdown-btn" onclick="toggleSearchDropdown()">
                                <span id="searchDropdownText">Geniuses</span>
                                <i class="fas fa-chevron-down"></i>
                            </button>
                            <div class="search-dropdown-menu" id="searchDropdownMenu">
                                <div class="search-dropdown-item" onclick="selectSearchType('Geniuses')">
                                    <i class="fas fa-user-graduate"></i>
                                    <span>Geniuses</span>
                                </div>
                                <div class="search-dropdown-item" onclick="selectSearchType('Jobs')">
                                    <i class="fas fa-briefcase"></i>
                                    <span>Jobs</span>
                                </div>
                                <div class="search-dropdown-item" onclick="selectSearchType('Projects')">
                                    <i class="fas fa-project-diagram"></i>
                                    <span>Projects</span>
                                </div>
                            </div>
                        </div>
                        <input type="text" id="searchInput" placeholder="Search for Geniuses">
                        <i class="fas fa-search icon"></i>
                    </div>
                </div>
                <div class="auth-buttons">
                    <div class="notification-icon">
                        <i class="fas fa-bell"></i>
                        <span id="notification-count">0</span>
                    </div>
                    <div class="notification-dropdown">
                        <div class="notification-header">
                            <span>Notifications</span>
                            <span class="notification-header-actions">Mark all as read</span>
                        </div>
                        <div id="notification-list">
                            <!-- Notifications will be loaded here -->
                        </div>
                        <div id="empty-notifications" class="empty-notifications" style="display: none;">
                            <i class="far fa-bell-slash"></i>
                            <p>No notifications yet</p>
                        </div>
                    </div>
                    <div class="profile-dropdown">
                        <div class="profile-button">
                            <img src="{{ url_for('api_profile_photo', user_type='client', user_id=session.get('user_id')) }}" alt="Profile Picture">
                        </div>
                        <div class="profile-dropdown-content">
                            <a href="{{ url_for('landing_page') }}">
                                <i class="fas fa-user"></i> My Profile
                            </a>
                            <a href="{{ url_for('landing_page') }}">
                                <i class="fas fa-cog"></i> Account Settings
                            </a>
                            <div class="dropdown-divider"></div>
                            <a href="{{ url_for('logout') }}" class="logout-option">
                                <i class="fas fa-sign-out-alt"></i> Log Out
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Page Header Section -->
        <div class="page-header">
            <h1><i class="fas fa-file-contract"></i> All Contracts</h1>
            <p>Manage and track all your contracts in one place</p>
        </div>

        <!-- Statistics Cards -->
        <div class="stats-grid">
            <div class="stat-card active">
                <div class="icon">
                    <i class="fas fa-play-circle"></i>
                </div>
                <h3>12</h3>
                <p>Active Contracts</p>
            </div>
            <div class="stat-card completed">
                <div class="icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h3>45</h3>
                <p>Completed Contracts</p>
            </div>
            <div class="stat-card pending">
                <div class="icon">
                    <i class="fas fa-clock"></i>
                </div>
                <h3>8</h3>
                <p>Pending Approval</p>
            </div>
            <div class="stat-card cancelled">
                <div class="icon">
                    <i class="fas fa-times-circle"></i>
                </div>
                <h3>3</h3>
                <p>Cancelled</p>
            </div>
        </div>

        <!-- Filters Section -->
        <div class="filters-section">
            <div class="filters-grid">
                <div class="filter-group">
                    <label for="statusFilter">Status</label>
                    <select id="statusFilter" class="filter-select">
                        <option value="">All Statuses</option>
                        <option value="active">Active</option>
                        <option value="completed">Completed</option>
                        <option value="pending">Pending</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="clientFilter">Client</label>
                    <select id="clientFilter" class="filter-select">
                        <option value="">All Clients</option>
                        <option value="client1">TechCorp Inc.</option>
                        <option value="client2">Design Studio</option>
                        <option value="client3">StartupXYZ</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="dateFilter">Date Range</label>
                    <input type="date" id="dateFilter" class="filter-input">
                </div>
                <div class="filter-group">
                    <label for="searchFilter">Search</label>
                    <input type="text" id="searchFilter" class="filter-input" placeholder="Search contracts...">
                </div>
                <div class="filter-group">
                    <label>&nbsp;</label>
                    <button class="search-btn">
                        <i class="fas fa-search"></i> Filter
                    </button>
                </div>
            </div>
        </div>

        <!-- Contracts Section -->
        <div class="contracts-section">
            <div class="section-header">
                <h2 class="section-title">Contract List</h2>
            </div>

            <!-- Contract Cards -->
            <div class="contracts-list">
                <!-- Contract 1 - Active -->
                <div class="contract-card">
                    <div class="contract-header">
                        <div>
                            <div class="contract-title">Website Development Project</div>
                            <div class="contract-id">Contract #CT-2024-001</div>
                        </div>
                        <span class="status-badge active">Active</span>
                    </div>
                    <div class="contract-details">
                        <div class="detail-item">
                            <span class="detail-label">Client</span>
                            <span class="detail-value">TechCorp Inc.</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Value</span>
                            <span class="detail-value">$5,500</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Start Date</span>
                            <span class="detail-value">Jan 15, 2024</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">End Date</span>
                            <span class="detail-value">Mar 15, 2024</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Progress</span>
                            <span class="detail-value">65%</span>
                        </div>
                    </div>
                    <div class="contract-actions">
                        <button class="action-btn btn-primary">
                            <i class="fas fa-eye"></i> View Details
                        </button>
                        <button class="action-btn btn-secondary">
                            <i class="fas fa-edit"></i> Edit
                        </button>
                        <button class="action-btn btn-secondary">
                            <i class="fas fa-download"></i> Download
                        </button>
                    </div>
                </div>

                <!-- Contract 2 - Completed -->
                <div class="contract-card">
                    <div class="contract-header">
                        <div>
                            <div class="contract-title">Mobile App UI Design</div>
                            <div class="contract-id">Contract #CT-2024-002</div>
                        </div>
                        <span class="status-badge completed">Completed</span>
                    </div>
                    <div class="contract-details">
                        <div class="detail-item">
                            <span class="detail-label">Client</span>
                            <span class="detail-value">Design Studio</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Value</span>
                            <span class="detail-value">$3,200</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Start Date</span>
                            <span class="detail-value">Dec 1, 2023</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">End Date</span>
                            <span class="detail-value">Jan 10, 2024</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Progress</span>
                            <span class="detail-value">100%</span>
                        </div>
                    </div>
                    <div class="contract-actions">
                        <button class="action-btn btn-primary">
                            <i class="fas fa-eye"></i> View Details
                        </button>
                        <button class="action-btn btn-secondary">
                            <i class="fas fa-download"></i> Download
                        </button>
                        <button class="action-btn btn-secondary">
                            <i class="fas fa-star"></i> Rate
                        </button>
                    </div>
                </div>

                <!-- Contract 3 - Pending -->
                <div class="contract-card">
                    <div class="contract-header">
                        <div>
                            <div class="contract-title">Content Writing Package</div>
                            <div class="contract-id">Contract #CT-2024-003</div>
                        </div>
                        <span class="status-badge pending">Pending</span>
                    </div>
                    <div class="contract-details">
                        <div class="detail-item">
                            <span class="detail-label">Client</span>
                            <span class="detail-value">StartupXYZ</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Value</span>
                            <span class="detail-value">$1,800</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Start Date</span>
                            <span class="detail-value">Feb 1, 2024</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">End Date</span>
                            <span class="detail-value">Feb 28, 2024</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Progress</span>
                            <span class="detail-value">0%</span>
                        </div>
                    </div>
                    <div class="contract-actions">
                        <button class="action-btn btn-primary">
                            <i class="fas fa-check"></i> Accept
                        </button>
                        <button class="action-btn btn-danger">
                            <i class="fas fa-times"></i> Decline
                        </button>
                        <button class="action-btn btn-secondary">
                            <i class="fas fa-eye"></i> View Details
                        </button>
                    </div>
                </div>

                <!-- Contract 4 - Cancelled -->
                <div class="contract-card">
                    <div class="contract-header">
                        <div>
                            <div class="contract-title">E-commerce Platform Development</div>
                            <div class="contract-id">Contract #CT-2023-045</div>
                        </div>
                        <span class="status-badge cancelled">Cancelled</span>
                    </div>
                    <div class="contract-details">
                        <div class="detail-item">
                            <span class="detail-label">Client</span>
                            <span class="detail-value">RetailCorp</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Value</span>
                            <span class="detail-value">$8,500</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Start Date</span>
                            <span class="detail-value">Nov 15, 2023</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">End Date</span>
                            <span class="detail-value">Jan 30, 2024</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Progress</span>
                            <span class="detail-value">25%</span>
                        </div>
                    </div>
                    <div class="contract-actions">
                        <button class="action-btn btn-secondary">
                            <i class="fas fa-eye"></i> View Details
                        </button>
                        <button class="action-btn btn-secondary">
                            <i class="fas fa-archive"></i> Archive
                        </button>
                    </div>
                </div>
            </div>

            <!-- No Contracts State (hidden by default) -->
            <div class="no-contracts" style="display: none;">
                <i class="fas fa-file-contract"></i>
                <h3>No Contracts Found</h3>
                <p>You don't have any contracts yet. Start by applying to jobs or creating proposals.</p>
            </div>
        </div>
    </div>

    <script>
        // Navigation dropdown functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Mobile menu toggle
            const mobileMenuBtn = document.getElementById('mobileMenuBtn');
            const mobileCloseBtn = document.getElementById('mobileCloseBtn');
            const navLinks = document.getElementById('navLinks');

            function openMobileMenu() {
                navLinks.classList.add('active');
            }

            function closeMobileMenu() {
                navLinks.classList.remove('active');
            }

            if (mobileMenuBtn) {
                mobileMenuBtn.addEventListener('click', function() {
                    openMobileMenu();
                });
            }

            if (mobileCloseBtn) {
                mobileCloseBtn.addEventListener('click', function() {
                    closeMobileMenu();
                });
            }

            // Mobile dropdown toggles
            const navDropdowns = document.querySelectorAll('.nav-dropdown');

            navDropdowns.forEach(dropdown => {
                const dropBtn = dropdown.querySelector('.nav-dropbtn');

                dropBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    // Toggle current dropdown
                    dropdown.classList.toggle('active');

                    // Close other dropdowns
                    navDropdowns.forEach(otherDropdown => {
                        if (otherDropdown !== dropdown) {
                            otherDropdown.classList.remove('active');
                            const otherIcon = otherDropdown.querySelector('.nav-dropbtn i');
                            if (otherIcon) {
                                otherIcon.classList.remove('fa-chevron-up');
                                otherIcon.classList.add('fa-chevron-down');
                            }
                        }
                    });

                    // Change dropdown icon
                    const icon = this.querySelector('i');
                    if (dropdown.classList.contains('active')) {
                        icon.classList.remove('fa-chevron-down');
                        icon.classList.add('fa-chevron-up');
                    } else {
                        icon.classList.remove('fa-chevron-up');
                        icon.classList.add('fa-chevron-down');
                    }
                });
            });

            // Close navigation dropdowns when clicking outside
            document.addEventListener('click', function(e) {
                const navDropdowns = document.querySelectorAll('.nav-dropdown');
                let clickedInsideDropdown = false;

                navDropdowns.forEach(dropdown => {
                    if (dropdown.contains(e.target)) {
                        clickedInsideDropdown = true;
                    }
                });

                if (!clickedInsideDropdown) {
                    navDropdowns.forEach(dropdown => {
                        dropdown.classList.remove('active');
                        const icon = dropdown.querySelector('.nav-dropbtn i');
                        if (icon) {
                            icon.classList.remove('fa-chevron-up');
                            icon.classList.add('fa-chevron-down');
                        }
                    });
                }
            });

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth > 992 && navLinks.classList.contains('active')) {
                    closeMobileMenu();

                    // Reset all dropdowns
                    navDropdowns.forEach(dropdown => {
                        dropdown.classList.remove('active');
                        const icon = dropdown.querySelector('.nav-dropbtn i');
                        icon.classList.remove('fa-chevron-up');
                        icon.classList.add('fa-chevron-down');
                    });
                }
            });
        });

        // Search dropdown functions
        function toggleSearchDropdown() {
            const menu = document.getElementById('searchDropdownMenu');
            const dropdown = document.querySelector('.search-dropdown');

            if (menu.classList.contains('show')) {
                menu.classList.remove('show');
                dropdown.classList.remove('active');
            } else {
                menu.classList.add('show');
                dropdown.classList.add('active');
            }
        }

        function selectSearchType(type) {
            document.getElementById('searchDropdownText').textContent = type;
            document.getElementById('searchDropdownMenu').classList.remove('show');
            document.querySelector('.search-dropdown').classList.remove('active');

            // Update search placeholder
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.placeholder = 'Search for ' + type;
            }
        }

        // Close search dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const searchDropdown = document.querySelector('.search-dropdown');
            const searchMenu = document.getElementById('searchDropdownMenu');

            if (searchDropdown && !searchDropdown.contains(event.target)) {
                searchMenu.classList.remove('show');
                searchDropdown.classList.remove('active');
            }
        });

        // Mobile search dropdown functions
        function toggleMobileSearchDropdown() {
            const dropdown = document.getElementById('mobileSearchDropdownMenu');
            dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
        }

        function selectMobileSearchType(type) {
            document.getElementById('mobileSearchDropdownText').textContent = type;
            document.getElementById('mobileSearchDropdownMenu').style.display = 'none';

            // Update placeholder
            const mobileSearchInput = document.getElementById('mobileSearchInput');
            if (mobileSearchInput) {
                mobileSearchInput.placeholder = `Search for ${type}`;
            }

            // Sync with desktop search dropdown if it exists
            const desktopDropdownText = document.getElementById('searchDropdownText');
            if (desktopDropdownText) {
                desktopDropdownText.textContent = type;
            }

            const desktopSearchInput = document.getElementById('searchInput');
            if (desktopSearchInput) {
                desktopSearchInput.placeholder = `Search for ${type}`;
            }
        }

        // Close mobile search dropdown when clicking outside
        document.addEventListener('click', function(e) {
            const mobileSearchDropdown = document.getElementById('mobileSearchDropdownMenu');
            const mobileSearchBtn = document.querySelector('.mobile-search-bar .search-dropdown-btn');

            if (mobileSearchDropdown && mobileSearchBtn &&
                !mobileSearchBtn.contains(e.target) &&
                !mobileSearchDropdown.contains(e.target)) {
                mobileSearchDropdown.style.display = 'none';
            }
        });

        // Notification dropdown functionality
        document.addEventListener('DOMContentLoaded', function() {
            const notificationIcon = document.querySelector('.notification-icon');
            const dropdown = document.querySelector('.notification-dropdown');

            notificationIcon.addEventListener('click', function(e) {
                e.stopPropagation();
                dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
            });

            document.addEventListener('click', function(e) {
                if (!notificationIcon.contains(e.target)) {
                    dropdown.style.display = 'none';
                }
            });

            // Mark all as read
            const markAllBtn = document.querySelector('.notification-header-actions');
            if (markAllBtn) {
                markAllBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    document.querySelectorAll('.notification-item.unread').forEach(item => {
                        item.classList.remove('unread');
                    });

                    document.querySelector('#notification-count').textContent = '0';
                });
            }
        });

        // Profile dropdown functionality
        document.addEventListener('DOMContentLoaded', function() {
            const profileDropdown = document.querySelector('.profile-dropdown');
            const profileButton = profileDropdown.querySelector('.profile-button');

            // Toggle dropdown on profile button click
            profileButton.addEventListener('click', function(e) {
                e.stopPropagation();
                profileDropdown.classList.toggle('active');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!profileDropdown.contains(e.target)) {
                    profileDropdown.classList.remove('active');
                }
            });
        });

        // Filter functionality
        document.addEventListener('DOMContentLoaded', function() {
            const filterBtn = document.querySelector('.search-btn');
            const statusFilter = document.getElementById('statusFilter');
            const clientFilter = document.getElementById('clientFilter');
            const searchFilter = document.getElementById('searchFilter');
            const contractCards = document.querySelectorAll('.contract-card');

            function filterContracts() {
                const statusValue = statusFilter.value.toLowerCase();
                const clientValue = clientFilter.value.toLowerCase();
                const searchValue = searchFilter.value.toLowerCase();

                contractCards.forEach(card => {
                    const status = card.querySelector('.status-badge').textContent.toLowerCase();
                    const client = card.querySelector('.detail-value').textContent.toLowerCase();
                    const title = card.querySelector('.contract-title').textContent.toLowerCase();

                    const statusMatch = !statusValue || status.includes(statusValue);
                    const clientMatch = !clientValue || client.includes(clientValue);
                    const searchMatch = !searchValue || title.includes(searchValue) || client.includes(searchValue);

                    if (statusMatch && clientMatch && searchMatch) {
                        card.style.display = 'block';
                    } else {
                        card.style.display = 'none';
                    }
                });
            }

            filterBtn.addEventListener('click', filterContracts);
            searchFilter.addEventListener('input', filterContracts);
            statusFilter.addEventListener('change', filterContracts);
            clientFilter.addEventListener('change', filterContracts);

            // Action button handlers
            document.querySelectorAll('.action-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const action = this.textContent.trim();
                    const contractTitle = this.closest('.contract-card').querySelector('.contract-title').textContent;

                    // Add your action handlers here
                    console.log(`Action: ${action} on contract: ${contractTitle}`);

                    // Example: Show alert for demo
                    alert(`${action} clicked for: ${contractTitle}`);
                });
            });
        });
    </script>
</body>
</html>
